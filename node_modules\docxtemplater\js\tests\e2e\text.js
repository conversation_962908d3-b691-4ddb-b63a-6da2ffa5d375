"use strict";

function _regenerator() { /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/babel/babel/blob/main/packages/babel-helpers/LICENSE */ var e, t, r = "function" == typeof Symbol ? Symbol : {}, n = r.iterator || "@@iterator", o = r.toStringTag || "@@toStringTag"; function i(r, n, o, i) { var c = n && n.prototype instanceof Generator ? n : Generator, u = Object.create(c.prototype); return _regeneratorDefine2(u, "_invoke", function (r, n, o) { var i, c, u, f = 0, p = o || [], y = !1, G = { p: 0, n: 0, v: e, a: d, f: d.bind(e, 4), d: function d(t, r) { return i = t, c = 0, u = e, G.n = r, a; } }; function d(r, n) { for (c = r, u = n, t = 0; !y && f && !o && t < p.length; t++) { var o, i = p[t], d = G.p, l = i[2]; r > 3 ? (o = l === n) && (u = i[(c = i[4]) ? 5 : (c = 3, 3)], i[4] = i[5] = e) : i[0] <= d && ((o = r < 2 && d < i[1]) ? (c = 0, G.v = n, G.n = i[1]) : d < l && (o = r < 3 || i[0] > n || n > l) && (i[4] = r, i[5] = n, G.n = l, c = 0)); } if (o || r > 1) return a; throw y = !0, n; } return function (o, p, l) { if (f > 1) throw TypeError("Generator is already running"); for (y && 1 === p && d(p, l), c = p, u = l; (t = c < 2 ? e : u) || !y;) { i || (c ? c < 3 ? (c > 1 && (G.n = -1), d(c, u)) : G.n = u : G.v = u); try { if (f = 2, i) { if (c || (o = "next"), t = i[o]) { if (!(t = t.call(i, u))) throw TypeError("iterator result is not an object"); if (!t.done) return t; u = t.value, c < 2 && (c = 0); } else 1 === c && (t = i["return"]) && t.call(i), c < 2 && (u = TypeError("The iterator does not provide a '" + o + "' method"), c = 1); i = e; } else if ((t = (y = G.n < 0) ? u : r.call(n, G)) !== a) break; } catch (t) { i = e, c = 1, u = t; } finally { f = 1; } } return { value: t, done: y }; }; }(r, o, i), !0), u; } var a = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} t = Object.getPrototypeOf; var c = [][n] ? t(t([][n]())) : (_regeneratorDefine2(t = {}, n, function () { return this; }), t), u = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(c); function f(e) { return Object.setPrototypeOf ? Object.setPrototypeOf(e, GeneratorFunctionPrototype) : (e.__proto__ = GeneratorFunctionPrototype, _regeneratorDefine2(e, o, "GeneratorFunction")), e.prototype = Object.create(u), e; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, _regeneratorDefine2(u, "constructor", GeneratorFunctionPrototype), _regeneratorDefine2(GeneratorFunctionPrototype, "constructor", GeneratorFunction), GeneratorFunction.displayName = "GeneratorFunction", _regeneratorDefine2(GeneratorFunctionPrototype, o, "GeneratorFunction"), _regeneratorDefine2(u), _regeneratorDefine2(u, o, "Generator"), _regeneratorDefine2(u, n, function () { return this; }), _regeneratorDefine2(u, "toString", function () { return "[object Generator]"; }), (_regenerator = function _regenerator() { return { w: i, m: f }; })(); }
function _regeneratorDefine2(e, r, n, t) { var i = Object.defineProperty; try { i({}, "", {}); } catch (e) { i = 0; } _regeneratorDefine2 = function _regeneratorDefine(e, r, n, t) { if (r) i ? i(e, r, { value: n, enumerable: !t, configurable: !t, writable: !t }) : e[r] = n;else { var o = function o(r, n) { _regeneratorDefine2(e, r, function (e) { return this._invoke(r, n, e); }); }; o("next", 0), o("throw", 1), o("return", 2); } }, _regeneratorDefine2(e, r, n, t); }
function asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }
function _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, "next", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, "throw", n); } _next(void 0); }); }; }
var _require = require("../utils.js"),
  expect = _require.expect,
  expectToThrow = _require.expectToThrow,
  wrapMultiError = _require.wrapMultiError;
var Errors = require("../../errors.js");
var expressionParser = require("../../expressions.js");
var TxtTemplater = require("../../text.js");
describe("Text templating", function () {
  it("should be possible to template text files", function () {
    var doc = new TxtTemplater("Hello {user}, how are you ?");
    expect(doc.render({
      user: "John"
    })).to.be.equal("Hello John, how are you ?");
  });
  it("should not regress if data contains XML corrupt characters", function () {
    var doc = new TxtTemplater("Hello {user}, how are you ?");
    expect(doc.render({
      user: "John\x02"
    })).to.be.equal("Hello John\x02, how are you ?");
  });
  it("should be possible to template text files with expressionParser", function () {
    var doc = new TxtTemplater("Hello {user + age}, how are you ?", {
      parser: expressionParser
    });
    expect(doc.render({
      user: "John ",
      age: 12
    })).to.be.equal("Hello John 12, how are you ?");
  });
  it("should be possible to template xml files with expressionParser", function () {
    var doc = new TxtTemplater("<t>&gt;  {user}</t>", {
      parser: expressionParser
    });
    expect(doc.render({
      user: "<zaza> ",
      age: 12
    })).to.be.equal("<t>&gt;  <zaza> </t>");
  });
  it("should be possible to use loops", function () {
    var doc = new TxtTemplater("Hello {#users}{name},{/users} how are you ?", {
      parser: expressionParser
    });
    expect(doc.render({
      users: [{
        name: "John"
      }, {
        name: "Baz"
      }]
    })).to.be.equal("Hello John,Baz, how are you ?");
  });
  it("should throw specific error if loop not closed", function () {
    var expectedError = wrapMultiError({
      name: "TemplateError",
      message: "Unclosed loop",
      properties: {
        id: "unclosed_loop",
        xtag: "users",
        offset: 6
      }
    });
    expectToThrow(function () {
      return new TxtTemplater("Hello {#users}");
    }, Errors.XTTemplateError, expectedError);
  });
  it("should work with xml-namespace", function () {
    var doc = new TxtTemplater('<?xml version="1.0" encoding="UTF-8”?> Hello {name}');
    expect(doc.render({
      name: "John"
    })).to.be.equal('<?xml version="1.0" encoding="UTF-8”?> Hello John');
  });
  it("should not regress with paragraphLoop: true or linebreaks: true", function () {
    var doc = new TxtTemplater("Text {#users}{name}{/}", {
      paragraphLoop: true,
      linebreaks: true
    });
    expect(doc.render({
      users: [{
        name: "John\nFoo"
      }]
    })).to.be.equal("Text John\nFoo");
  });
  it("should be possible to render special characters in the output", function () {
    var doc = new TxtTemplater("Text {name}", {
      paragraphLoop: true,
      linebreaks: true
    });
    expect(doc.render({
      name: "&& <n>Baz</n> &nbsp;"
    })).to.be.equal("Text && <n>Baz</n> &nbsp;");
  });
  it("should be possible to use < and > as delimiters", function () {
    var doc = new TxtTemplater("Hello <name>", {
      delimiters: {
        start: "<",
        end: ">"
      }
    });
    expect(doc.render({
      name: "John"
    })).to.be.equal("Hello John");
  });
  it("should throw error if rendering error occurs", function () {
    var doc = new TxtTemplater("Hello <name>", {
      delimiters: {
        start: "<",
        end: ">"
      },
      parser: function parser(tag) {
        return {
          get: function get() {
            throw new Error("Error in get for ".concat(tag));
          }
        };
      }
    });
    var expectedError = {
      name: "ScopeParserError",
      message: "Scope parser execution failed",
      properties: {
        id: "scopeparser_execution_failed",
        scope: {
          name: "John"
        },
        xtag: "name",
        offset: 6,
        rootError: {
          message: "Error in get for name"
        }
      }
    };
    expectToThrow(function () {
      doc.render({
        name: "John"
      });
    }, Errors.XTTemplateError, wrapMultiError(expectedError));
  });
  describe("should work asynchronously", /*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regenerator().m(function _callee() {
    var doc, result;
    return _regenerator().w(function (_context) {
      while (1) switch (_context.n) {
        case 0:
          doc = new TxtTemplater("Hello {name}", {});
          _context.n = 1;
          return doc.renderAsync({
            name: new Promise(function (resolve) {
              resolve("John");
            })
          });
        case 1:
          result = _context.v;
          expect(result).to.equal("Hello John");
        case 2:
          return _context.a(2);
      }
    }, _callee);
  })));
});