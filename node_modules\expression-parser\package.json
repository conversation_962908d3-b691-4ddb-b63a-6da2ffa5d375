{"name": "expression-parser", "repository": "**************:uniphil/expression-parser.git", "version": "1.0.0", "description": "", "main": "func.js", "scripts": {"test": "gulp test && npm run coverage", "coverage": "istanbul cover ./node_modules/mocha/bin/_mocha --report lcovonly; cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js && rm -rf ./coverage"}, "author": "uniphil", "license": "BSD", "devDependencies": {"chai": "^1.9.2", "coveralls": "^2.11.2", "gulp": "^3.8.8", "gulp-jscs": "^1.2.0", "gulp-jshint": "^1.8.5", "gulp-load-plugins": "^0.7.0", "gulp-mocha": "^1.1.1", "istanbul": "^0.3.4", "jshint-stylish": "^1.0.0", "mocha": "^1.21.4"}, "dependencies": {"ramda": "^0.8.0"}}