import psycopg2
from psycopg2.extras import RealDictCursor
import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def obter_concessionaria_completa(nome_curto):
    """Converte nome curto da concessionária para nome completo"""
    mapeamento_concessionarias = {
        'Autoban': 'Concessionária do Sistema Anhanguera - Bandeirantes S/A',
        'Tebe': 'Concessionária de Rodovias Tebe S/A',
        'Intervias': 'Concessionária de Rodovias do Interior Paulista S/A Intervias',
        'Rota das Bandeiras': 'Concessionária Rota das Bandeiras S/A',
        'Triângulo do Sol': 'Concessionária Triângulo do Sol Auto-Estradas S/A',
        'Renovias': 'Renovias Concessionária S/A',
        'Viaoeste': 'Concessionária de Rodovias do Oeste de São Paulo - Viaoeste S/A',
        'Colinas': 'Concessionária Rodovias das Colinas S/A',
        'CART': 'Concessionária Auto Raposo Tavares S/A',
        'ViaRondon': 'Viarondon Concessionária de Rodovia S/A',
        'Spvias': 'Concessionária Rodovias Integradas do Oeste S/A',
        'Rodovias do Tietê': 'Concessionária Rodovias do Tietê S/A',
        'Ecovias': 'Concessionária Ecovias dos Imigrantes S/A',
        'Ecovias Imigrantes': 'Concessionária Ecovias dos Imigrantes S/A',
        'Ecovias Leste Paulista': 'Concessionária Ecovias Leste Paulista S/A',
        'Rodoanel Oeste': 'Concessionária do Rodoanel Oeste S/A',
        'SPMAR': 'Concessionária Litoral Sul S/A',
        'ViaSPSerra': 'Concessionária Rodoanel Norte',
        'Tamoios': 'Concessionária Rodovias dos Tamoios S/A',
        'Entrevias': 'Entrevias Concessionária de Rodovias S/A',
        'ViaPaulista': 'Concessionária Viapaulista S/A',
        'EixoSP': 'Concessionária de Rodovias Piracicaba Panorama S/A - EIXO SP',
        'Ecovias Noroeste Paulista': 'Concessionária Ecovias Noroeste Paulista S.A',
        'Novo Litoral': 'Companhia de Concessões Rodoviárias do Novo Litoral de São Paulo',
        'Rota Sorocabana': 'Concessionária Rota Sorocabana S.A',
        'Ecovias Raposo Castello': 'Concessionária Ecovias Raposo-Castello S.A'
    }
    
    return mapeamento_concessionarias.get(nome_curto, nome_curto)

def testar_mapeamento():
    """Testa o mapeamento de concessionárias"""
    try:
        # Conecta ao banco
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT'),
            database=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD')
        )
        
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Busca a concessionária atual do banco
            cur.execute("SELECT concessionaria FROM vistorias WHERE id = 7")
            resultado = cur.fetchone()
            
            if resultado:
                nome_banco = resultado['concessionaria']
                nome_completo = obter_concessionaria_completa(nome_banco)
                
                print(f"=== TESTE DE MAPEAMENTO ===")
                print(f"Nome no banco: '{nome_banco}'")
                print(f"Nome completo: '{nome_completo}'")
                print(f"Mapeamento funcionou: {'✅ SIM' if nome_completo != nome_banco else '❌ NÃO'}")
                
                # Testa alguns outros valores
                print(f"\n=== OUTROS TESTES ===")
                testes = ['Autoban', 'Tebe', 'Renovias', 'CART']
                for teste in testes:
                    completo = obter_concessionaria_completa(teste)
                    print(f"'{teste}' → '{completo}'")
            else:
                print("❌ Nenhum registro encontrado com ID 7")
        
        conn.close()
        
    except Exception as e:
        print(f"Erro: {e}")

if __name__ == "__main__":
    testar_mapeamento()
