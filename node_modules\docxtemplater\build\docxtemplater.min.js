!function(){var e={60:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,a(n.key),n)}}function a(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var i=r(536),s=r(207).isContent,l=r(946),u=l.throwRawTagShouldBeOnlyTextInParagraph,p=l.getInvalidRawXMLValueException,c=r(899),f="rawxml";function h(e){for(var t=e.part,r=e.left,n=e.right,o=e.postparsed,a=e.index,i=o.slice(r+1,n),l=0,p=i.length;l<p;l++)if(l!==a-r-1){var c=i[l];s(c)&&u({paragraphParts:i,part:t})}return t}var d=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="RawXmlModule",this.prefix="@"},t=[{key:"optionsTransformer",value:function(e,t){return this.fileTypeConfig=t.fileTypeConfig,e}},{key:"matchers",value:function(){return[[this.prefix,f]]}},{key:"postparse",value:function(e){return i.expandToOne(e,{moduleName:f,getInner:h,expandTo:this.fileTypeConfig.tagRawXml,error:{message:"Raw tag not in paragraph",id:"raw_tag_outerxml_invalid",explanation:function(e){return'The tag "'.concat(e.value,'" is not inside a paragraph, putting raw tags inside an inline loop is disallowed.')}}})}},{key:"render",value:function(e,t){if(e.module!==f)return null;var r,n=[];try{null!=(r=t.scopeManager.getValue(e.value,{part:e}))||(r=t.nullGetter(e))}catch(e){return n.push(e),{errors:n}}return"string"==typeof(r=r||"")?{value:r}:{errors:[p({tag:e.value,value:r,offset:e.offset})]}}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return c(new d)}},181:function(e,t,r){var n=r(207).str2xml,o="[Content_Types].xml";e.exports={collectContentTypes:function(e,t,r){for(var n={},a=0;a<e.length;a++){var i=e[a],s=i.getAttribute("ContentType"),l=i.getAttribute("PartName").substr(1);n[l]=s}for(var u=function(){var e=t[p],a=e.getAttribute("ContentType"),i=e.getAttribute("Extension");r.file(/./).map((function(e){var t=e.name;t.slice(t.length-i.length)!==i||n[t]||t===o||(n[t]=a)}))},p=0;p<t.length;p++)u();return n},getContentTypes:function(e){var t=e.files[o],r=t?n(t.asText()):null;return{overrides:r?r.getElementsByTagName("Override"):null,defaults:r?r.getElementsByTagName("Default"):null,contentTypes:t,contentTypeXml:r}}}},183:function(e){function t(e){var t,r,n,o,a=0,i=e.length;for(n=0;n<i;n++)55296==(64512&(t=e.charCodeAt(n)))&&n+1<i&&56320==(64512&(r=e.charCodeAt(n+1)))&&(t=65536+(t-55296<<10)+(r-56320),n++),a+=t<128?1:t<2048?2:t<65536?3:4;var s=new Uint8Array(a);for(o=0,n=0;o<a;n++)55296==(64512&(t=e.charCodeAt(n)))&&n+1<i&&56320==(64512&(r=e.charCodeAt(n+1)))&&(t=65536+(t-55296<<10)+(r-56320),n++),t<128?s[o++]=t:t<2048?(s[o++]=192|t>>>6,s[o++]=128|63&t):t<65536?(s[o++]=224|t>>>12,s[o++]=128|t>>>6&63,s[o++]=128|63&t):(s[o++]=240|t>>>18,s[o++]=128|t>>>12&63,s[o++]=128|t>>>6&63,s[o++]=128|63&t);return s}e.exports=function(e,r){for(var n=0,o=r.modules;n<o.length;n++)e=o[n].postrender(e,r);for(var a=0,i=r.joinUncorrupt(e,r),s="",l=0,u=[],p=0,c=i.length;p<c;p++){var f=i[p];if(f.length+l>65536){var h=t(s);a+=h.length,u.push(h),s=""}s+=f,l+=f.length,delete i[p]}var d=t(s);a+=d.length,u.push(d);for(var v=new Uint8Array(a),g=0,m=0;m<u.length;m++){for(var y=u[m],b=0;b<y.length;++b)v[b+g]=y[b];g+=y.length}return v}},201:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,a(n.key),n)}}function a(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var i=r(798),s=r(207),l=s.getLeft,u=s.getRight,p=s.pushArray,c=r(899),f=r(536).getExpandToDefault,h=r(946),d=h.getUnmatchedLoopException,v=h.getClosingTagNotMatchOpeningTag,g=h.getUnbalancedLoopException;function m(e){switch(e.location){case"start":return 1;case"end":return-1}}function y(e,t){return null!=e&&null!=t&&("start"===e.part.location&&"end"===t.part.location&&e.part.value===t.part.value||""===t.part.value)}function b(e){for(var t=0,r=[];t<e.length;){var n=e[t].part;if("end"===n.location){if(0===t)return e.splice(0,1),r.push(d(n)),{traits:e,errors:r};var o=t,a=t-1,i=1;if(y(e[a],e[o]))return e.splice(o,1),e.splice(a,1),{errors:r,traits:e};for(;i<50;){var s=e[a-i],l=e[o+i];if(y(s,e[o]))return e.splice(o,1),e.splice(a-i,1),{errors:r,traits:e};if(y(e[a],l))return e.splice(o+i,1),e.splice(a,1),{errors:r,traits:e};i++}return r.push(v({tags:[e[a].part,e[o].part]})),e.splice(o,1),e.splice(a,1),{traits:e,errors:r}}t++}for(var u=0;u<e.length;u++){var p=e[u].part;r.push(d(p))}return{traits:[],errors:r}}var x=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="ExpandPairTrait"},t=[{key:"optionsTransformer",value:function(e,t){return t.options.paragraphLoop&&p(t.fileTypeConfig.expandTags,t.fileTypeConfig.onParagraphLoop),this.expandTags=t.fileTypeConfig.expandTags,e}},{key:"postparse",value:function(e,t){var r=this,n=t.getTraits,o=t.postparse,a=t.fileType,s=n("expandPair",e);s=s.map((function(e){return e||[]}));var c=function(e){var t={},r=[],n=[],o=[];for(p(o,e);o.length>0;){var a=b(o);p(r,a.errors),o=a.traits}if(r.length>0)return{pairs:n,errors:r};for(var i=0,s=0;s<e.length;s++){var l=e[s],u=m(l.part);if(i+=u,1===u)t[i]=l;else{var c=t[i+1];0===i&&n.push([c,l])}i=i>=0?i:0}return{pairs:n,errors:r}}(s=i(s)),h=c.pairs,d=c.errors,v=0,y=null,x=h.map((function(t){var n,o,i=t[0].part.expandTo;if("auto"===i&&"text"!==a){var s=f(e,t,r.expandTags);s.error&&d.push(s.error),i=s.value}if(!i||"text"===a){var p=t[0].offset,c=t[1].offset;return p<v&&!r.docxtemplater.options.syntax.allowUnbalancedLoops&&d.push(g(t,y)),y=t,v=c,[p,c]}try{n=l(e,i,t[0].offset)}catch(e){d.push(e)}try{o=u(e,i,t[1].offset)}catch(e){d.push(e)}return n<v&&!r.docxtemplater.options.syntax.allowUnbalancedLoops&&d.push(g(t,y)),v=o,y=t,[n,o]}));if(d.length>0)return{postparsed:e,errors:d};var w,T=0;return{postparsed:e.reduce((function(t,r,n){var a=T<h.length&&x[T][0]<=n&&n<=x[T][1],i=h[T],s=x[T];if(!a)return t.push(r),t;if(s[0]===n&&(w=[]),i[0].offset!==n&&i[1].offset!==n&&w.push(r),s[1]===n){var l=e[i[0].offset];l.subparsed=o(w,{basePart:l}),l.endLindex=i[1].part.lIndex,delete l.location,delete l.expandTo,t.push(l),T++;for(var u=x[T];u&&u[0]<n;)T++,u=x[T]}return t}),[]),errors:d}}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return c(new x)}},207:function(e,t,r){function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var o=r(673),a=o.DOMParser,i=o.XMLSerializer,s=r(946).throwXmlTagNotFound,l=r(320),u=l.last,p=l.first;function c(e){return{get:function(t){return"."===e?t:t?t[e]:t}}}var f={},h=[["&","&amp;"],["<","&lt;"],[">","&gt;"],['"',"&quot;"],["'","&apos;"]],d=h.map((function(e){var t,r,o=(r=2,function(e){if(Array.isArray(e))return e}(t=e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],l=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return s}}(t,r)||function(e,t){if(e){if("string"==typeof e)return n(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?n(e,t):void 0}}(t,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),a=o[0],i=o[1];return{rstart:new RegExp(i,"g"),rend:new RegExp(a,"g"),start:i,end:a}})),v=new RegExp(String.fromCharCode(160),"g");function g(e,t){return e==="</"+t+">"}function m(e,t){return 0===e.indexOf("<"+t)&&-1!==[">"," ","/"].indexOf(e[t.length+1])}function y(e,t,r){"string"==typeof t&&(t=[t]);for(var n=1,o=r,a=e.length;o<a;o++)for(var i=e[o],s=0,l=t;s<l.length;s++){var u=l[s];if(g(i.value,u)&&n--,m(i.value,u)&&n++,0===n)return o}return null}function b(e,t,r){"string"==typeof t&&(t=[t]);for(var n=1,o=r;o>=0;o--)for(var a=e[o],i=0,s=t;i<s.length;i++){var l=s[i];if(m(a.value,l)&&n--,g(a.value,l)&&n++,0===n)return o}return null}var x=/[\x00-\x08\x0B\x0C\x0E-\x1F]/g;e.exports={endsWith:function(e,t){return-1!==e.indexOf(t,e.length-t.length)},startsWith:function(e,t){return e.substring(0,t.length)===t},isContent:function(e){var t=e.type,r=e.position;return"placeholder"===t||"content"===t&&"insidetag"===r},isParagraphStart:function(e){var t=e.type,r=e.tag,n=e.position;return-1!==["w:p","a:p"].indexOf(r)&&"tag"===t&&"start"===n},isParagraphEnd:function(e){var t=e.type,r=e.tag,n=e.position;return-1!==["w:p","a:p"].indexOf(r)&&"tag"===t&&"end"===n},isTagStart:function(e,t){var r=t.type,n=t.tag,o=t.position;return"tag"===r&&n===e&&("start"===o||"selfclosing"===o)},isTagEnd:function(e,t){var r=t.type,n=t.tag,o=t.position;return"tag"===r&&n===e&&"end"===o},isTextStart:function(e){var t=e.type,r=e.position;return e.text&&"tag"===t&&"start"===r},isTextEnd:function(e){var t=e.type,r=e.position;return e.text&&"tag"===t&&"end"===r},isStarting:m,isEnding:g,isModule:function(e,t){var r=e.module,n=e.type;return t instanceof Array||(t=[t]),"placeholder"===n&&-1!==t.indexOf(r)},uniq:function(e){for(var t={},r=[],n=0,o=e.length;n<o;++n)t[e[n]]||(t[e[n]]=!0,r.push(e[n]));return r},getDuplicates:function(e){for(var t=[],r={},n=[],o=0,a=e.length;o<a;++o)r[e[o]]?t.push(e[o]):(r[e[o]]=!0,n.push(e[o]));return t},chunkBy:function(e,t){for(var r=[[]],n=0;n<e.length;n++){var o=e[n],a=r[r.length-1],i=t(o);"start"===i?r.push([o]):"end"===i?(a.push(o),r.push([])):a.push(o)}for(var s=[],l=0;l<r.length;l++){var u=r[l];u.length>0&&s.push(u)}return s},last:u,first:p,xml2str:function(e){return(new i).serializeToString(e).replace(/xmlns(:[a-z0-9]+)?="" ?/g,"")},str2xml:function(e){return 65279===e.charCodeAt(0)&&(e=e.substr(1)),(new a).parseFromString(e,"text/xml")},getRightOrNull:y,getRight:function(e,t,r){var n=y(e,t,r);if(null!==n)return n;s({position:"right",element:t,parsed:e,index:r})},getLeftOrNull:b,getLeft:function(e,t,r){var n=b(e,t,r);if(null!==n)return n;s({position:"left",element:t,parsed:e,index:r})},pregMatchAll:function(e,t){for(var r,n=[];null!=(r=e.exec(t));)n.push({array:r,offset:r.index});return n},convertSpaces:function(e){return e.replace(v," ")},charMapRegexes:d,hasCorruptCharacters:function(e){return x.lastIndex=0,x.test(e)},removeCorruptCharacters:function(e){return"string"!=typeof e&&(e=String(e)),e.replace(x,"")},getDefaults:function(){return{errorLogging:"json",stripInvalidXMLChars:!1,paragraphLoop:!1,nullGetter:function(e){return e.module?"":"undefined"},xmlFileNames:["[Content_Types].xml"],parser:c,linebreaks:!1,fileTypeConfig:null,delimiters:{start:"{",end:"}"},syntax:{changeDelimiterPrefix:"="}}},wordToUtf8:function(e){for(var t=d.length-1;t>=0;t--){var r=d[t];e=e.replace(r.rstart,r.end)}return e},utf8ToWord:function(e){var t,r;e=null!==(t=e)&&void 0!==t&&t.toString?e.toString():"";for(var n=0,o=d.length;n<o;n++)r=d[n],e=e.replace(r.rend,r.start);return e},concatArrays:function(e){for(var t=[],r=0;r<e.length;r++)for(var n=e[r],o=0;o<n.length;o++){var a=n[o];t.push(a)}return t},pushArray:function(e,t){if(!t)return e;for(var r=0,n=t.length;r<n;r++)e.push(t[r]);return e},invertMap:function(e){var t={};for(var r in e){var n=e[r];t[n]||(t[n]=[]),t[n].push(r)}return t},charMap:h,getSingleAttribute:function(e,t){var r=e.indexOf(" ".concat(t,'="'));if(-1===r)return null;var n=e.substr(r).search(/["']/)+r,o=e.substr(n+1).search(/["']/)+n;return e.substr(n+1,o-n)},setSingleAttribute:function(e,t,r){var n;if(f[t]?n=f[t]:(n=new RegExp("(<.* ".concat(t,'=")([^"]*)(".*)$')),f[t]=n),n.test(e))return e.replace(n,"$1".concat(r,"$3"));var o=e.lastIndexOf("/>");return-1===o&&(o=e.lastIndexOf(">")),e.substr(0,o)+" ".concat(t,'="').concat(r,'"')+e.substr(o)},isWhiteSpace:function(e){return/^[ \n\r\t]+$/.test(e)},stableSort:function(e,t){return e.map((function(e,t){return{item:e,index:t}})).sort((function(e,r){return t(e.item,r.item)||e.index-r.index})).map((function(e){return e.item}))}}},208:function(e,t,r){var n=r(207),o=n.startsWith,a=n.endsWith,i=n.isStarting,s=n.isEnding,l=n.isWhiteSpace,u=r(322);e.exports=function(e,t){var r=t.fileTypeConfig.tagShouldContain||[],n="",p=-1;-1!==u.docx.indexOf(t.contentType)&&(e=function(e){for(var t="",r=0,n=e.length;r<n;r++){var i=e[r];l(i)||o(i,"<w:bookmarkEnd")||(a(t,"</w:tbl>")&&(o(i,"<w:p")||o(i,"<w:tbl")||o(i,"<w:sectPr")||(i="<w:p/>".concat(i))),t=i,e[r]=i)}return e}(e));for(var c=-1,f=0,h=r.length;f<h;f++)for(var d=r[f],v=d.tag,g=d.shouldContain,m=d.value,y=d.drop,b=d.dropParent,x=0,w=e.length;x<w;x++){var T=e[x];if(p===f){if(s(T,v))if(p=-1,b){for(var P=-1,O=c;O>0;O--)if(i(e[O],b)){P=O;break}for(var S=P;S<=e.length;S++){if(s(e[S],b)){e[S]="";break}e[S]=""}}else{for(var j=c;j<=x;j++)e[j]="";y||(e[x]=n+m+T)}n+=T;for(var E=0,C=g.length;E<C;E++){var k=g[E];if(i(T,k)){p=-1;break}}}-1===p&&i(T,v)&&-1===T.substr(1).indexOf("<")&&("/"===T[T.length-2]?e[x]="":(c=x,p=f,n=T))}return e}},245:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,a(n.key),n)}}function a(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var i=r(207),s=i.pushArray,l=i.wordToUtf8,u=i.convertSpaces,p=r(367),c=r(263),f=r(690),h=r(789),d=r(183),v=r(945),g=r(208);e.exports=function(){return e=function e(t,r){for(var n in function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.cachedParsers={},this.content=t,r)this[n]=r[n];this.setModules({inspect:{filePath:r.filePath}})},t=[{key:"resolveTags",value:function(e){var t=this;this.tags=e;var r=this.getOptions(),n=this.filePath;r.scopeManager=this.scopeManager,r.resolve=v;var o=[];return Promise.all(this.modules.map((function(e){return Promise.resolve(e.preResolve(r)).catch((function(e){o.push(e)}))}))).then((function(){if(0!==o.length)throw o;return v(r).then((function(e){var o=e.resolved,a=e.errors;if(0!==(a=a.map((function(e){var t;return e instanceof Error||(e=new Error(e)),(t=e).properties||(t.properties={}),e.properties.file=n,e}))).length)throw a;return Promise.all(o).then((function(e){return r.scopeManager.root.finishedResolving=!0,r.scopeManager.resolved=e,t.setModules({inspect:{resolved:e,filePath:n}}),e}))})).catch((function(e){throw t.errorChecker(e),e}))}))}},{key:"getFullText",value:function(){return e=this.content,t=this.fileTypeConfig.tagsXmlTextArray,r=p(e,t).matches.map((function(e){return e.array[2]})),l(u(r.join("")));var e,t,r}},{key:"setModules",value:function(e){for(var t=0,r=this.modules;t<r.length;t++)r[t].set(e)}},{key:"preparse",value:function(){this.allErrors=[],this.xmllexed=c.xmlparse(this.content,{text:this.fileTypeConfig.tagsXmlTextArray,other:this.fileTypeConfig.tagsXmlLexedArray}),this.setModules({inspect:{filePath:this.filePath,xmllexed:this.xmllexed}});var e=c.parse(this.xmllexed,this.delimiters,this.syntax,this.fileType),t=e.lexed,r=e.errors;s(this.allErrors,r),this.lexed=t,this.setModules({inspect:{filePath:this.filePath,lexed:this.lexed}});var n=this.getOptions();this.lexed=f.preparse(this.lexed,this.modules,n)}},{key:"parse",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).noPostParse;this.setModules({inspect:{filePath:this.filePath}});var t=this.getOptions();return this.parsed=f.parse(this.lexed,this.modules,t),this.setModules({inspect:{filePath:this.filePath,parsed:this.parsed}}),e?this:this.postparse()}},{key:"postparse",value:function(){var e=this.getOptions(),t=f.postparse(this.parsed,this.modules,e),r=t.postparsed,n=t.errors;return this.postparsed=r,this.setModules({inspect:{filePath:this.filePath,postparsed:this.postparsed}}),s(this.allErrors,n),this.errorChecker(this.allErrors),this}},{key:"errorChecker",value:function(e){for(var t=0,r=e;t<r.length;t++){var n=r[t];n.properties||(n.properties={}),n.properties.file=this.filePath}for(var o=0,a=this.modules;o<a.length;o++)e=a[o].errorsTransformer(e)}},{key:"baseNullGetter",value:function(e,t){for(var r=null,n=0,o=this.modules;n<o.length;n++){var a=o[n];null==r&&(r=a.nullGetter(e,t,this))}return null!=r?r:this.nullGetter(e,t)}},{key:"getOptions",value:function(){return{compiled:this.postparsed,cachedParsers:this.cachedParsers,tags:this.tags,modules:this.modules,parser:this.parser,contentType:this.contentType,relsType:this.relsType,baseNullGetter:this.baseNullGetter.bind(this),filePath:this.filePath,fileTypeConfig:this.fileTypeConfig,fileType:this.fileType,linebreaks:this.linebreaks,stripInvalidXMLChars:this.stripInvalidXMLChars}}},{key:"render",value:function(e){this.filePath=e;var t=this.getOptions();t.resolved=this.scopeManager.resolved,t.scopeManager=this.scopeManager,t.render=h,t.joinUncorrupt=g;var r=h(t),n=r.errors,o=r.parts;return n.length>0?(this.allErrors=n,this.errorChecker(n),this):(this.content=d(o,t),this.setModules({inspect:{filePath:this.filePath,content:this.content}}),this)}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}()},263:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],l=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return a(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach((function(t){l(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function l(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var u=r(946),p=u.getUnclosedTagException,c=u.getUnopenedTagException,f=u.getDuplicateOpenTagException,h=u.getDuplicateCloseTagException,d=u.throwMalformedXml,v=u.throwXmlInvalid,g=u.XTTemplateError,m=r(207),y=m.isTextStart,b=m.isTextEnd,x=m.wordToUtf8,w=m.pushArray;function T(e,t){return e[0]<=t.offset&&t.offset<e[1]}function P(e,t){return y(e)?(t&&d(),!0):b(e)?(t||d(),!1):t}function O(e){var t="",r=1,n=e.indexOf(" ");return"/"===e[e.length-2]?(t="selfclosing",-1===n&&(n=e.length-2)):"/"===e[1]?(r=2,t="end",-1===n&&(n=e.length-1)):(t="start",-1===n&&(n=e.length-1)),{tag:e.slice(r,n),position:t}}function S(e,t){return-1===e&&-1===t?0:e===t?1:-1===e||-1===t?t<e?2:3:e<t?2:3}function j(e){var t=e.split(" ");if(2!==t.length){var r=new g("New Delimiters cannot be parsed");throw r.properties={id:"change_delimiters_invalid",explanation:"Cannot parser delimiters"},r}var n=o(t,2),a=n[0],i=n[1];if(0===a.length||0===i.length){var s=new g("New Delimiters cannot be parsed");throw s.properties={id:"change_delimiters_invalid",explanation:"Cannot parser delimiters"},s}return[a,i]}function E(e,t,r){var n=e.map((function(e){return e.value})).join(""),a=function(e,t,r){var n=[],a=t.start,i=t.end,s=-1,l=!1;if(null==a&&null==i)return[];for(;;){var u=e.indexOf(a,s+1),p=e.indexOf(i,s+1),c=null,f=void 0,h=S(u,p);switch(1===h&&(h=l?3:2),h){case 0:return n;case 3:l=!1,s=p,c="end",f=i.length;break;case 2:l=!0,s=u,c="start",f=a.length}if(r.changeDelimiterPrefix&&2===h&&e[s+a.length]===r.changeDelimiterPrefix){n.push({offset:u,position:"start",length:a.length,changedelimiter:!0});var d=e.indexOf(r.changeDelimiterPrefix,s+a.length+1),v=e.indexOf(i,d+1);n.push({offset:v,position:"end",length:i.length,changedelimiter:!0});var g=o(j(e.substr(s+a.length+1,d-s-a.length-1)),2);a=g[0],i=g[1],s=v}else n.push({offset:s,position:c,length:f})}}(n,t,r),i=0,l=e.map((function(e){return{offset:(i+=e.value.length)-e.value.length,lIndex:e.lIndex}})),u=function(e,t,r){var n,o=[],a=!1,i={offset:0},l=e.reduce((function(e,l){var u=l.position,d=l.offset,v=i.offset,g=i.length;if(n=t.substr(v,d-v),a&&"start"===u){if(v+g===d&&(n=t.substr(v,d-v+g+4),!r.allowUnclosedTag))return o.push(f({xtag:n,offset:v})),i=l,e.push(s(s({},l),{},{error:!0})),e;if(!r.allowUnclosedTag)return o.push(p({xtag:x(n),offset:v})),i=l,e.push(s(s({},l),{},{error:!0})),e;e.pop()}return a||"end"!==u?(a="start"===u,i=l,e.push(l),e):r.allowUnopenedTag?e:v+g===d?(n=t.substr(v-4,d-v+g+4),o.push(h({xtag:n,offset:v})),i=l,e.push(s(s({},l),{},{error:!0})),e):(o.push(c({xtag:n,offset:d})),i=l,e.push(s(s({},l),{},{error:!0})),e)}),[]);if(a){var u=i.offset;n=t.substr(u,t.length-u),r.allowUnclosedTag?l.pop():o.push(p({xtag:x(n),offset:u}))}return{delimiterWithErrors:l,errors:o}}(a,n,r),d=u.delimiterWithErrors,v=u.errors,g=0,m=0,y=l.map((function(t,r){for(var n=t.offset,o=[n,n+e[r].value.length],a=e[r].value,i=[];m<d.length&&T(o,d[m]);)i.push(d[m]),m++;var s=[],l=0;g>0&&(l=g,g=0);for(var u=0;u<i.length;u++){var p=i[u],c=a.substr(l,p.offset-n-l);if(p.changedelimiter)"start"===p.position?c.length>0&&s.push({type:"content",value:c}):l=p.offset-n+p.length;else{c.length>0&&(s.push({type:"content",value:c}),l+=c.length);var f={type:"delimiter",position:p.position,offset:l+n};s.push(f),l=p.offset-n+p.length}}g=l-a.length;var h=a.substr(l);return h.length>0&&s.push({type:"content",value:h}),s}),this);return{parsed:y,errors:v}}function C(e){return"content"===e.type&&"insidetag"===e.position}e.exports={parseDelimiters:E,parse:function(e,t,r,n){!function(e,t){for(var r=!1,n=0;n<e.length;n++){var o=e[n];r=P(o,r),"content"===o.type&&(o.position=r?"insidetag":"outsidetag"),"text"!==t&&C(o)&&(o.value=o.value.replace(/>/g,"&gt;"))}}(e,n);for(var o=E(e.filter(C),t,r),a=o.parsed,i=o.errors,s=[],l=0,u=0,p=0;p<e.length;p++){var c=e[p];if(C(c)){for(var f=0,h=a[l];f<h.length;f++){var d=h[f];"content"===d.type&&(d.position="insidetag"),d.lIndex=u++}w(s,a[l]),l++}else c.lIndex=u++,s.push(c)}return{errors:i,lexed:s}},xmlparse:function(e,t){for(var r=function(e,t,r){for(var n=0,o=e.length,a={},i=0;i<t.length;i++)a[t[i]]=!0;for(var s=0;s<r.length;s++)a[r[s]]=!1;for(var l=[];n<o&&-1!==(n=e.indexOf("<",n));){var u=n,p=e.indexOf("<",n+1);(-1===(n=e.indexOf(">",n))||-1!==p&&n>p)&&v(e,u);var c=e.slice(u,n+1),f=O(c),h=f.tag,d=f.position,g=a[h];null!=g&&l.push({type:"tag",position:d,text:g,offset:u,value:c,tag:h})}return l}(e,t.text,t.other),n=0,o=[],a=0;a<r.length;a++){var i=r[a];e.length>n&&i.offset-n>0&&o.push({type:"content",value:e.substr(n,i.offset-n)}),n=i.offset+i.value.length,delete i.offset,o.push(i)}return e.length>n&&o.push({type:"content",value:e.substr(n)}),o}}},271:function(e,t,r){var n=r(885),o=r(522),a=r(60),i=r(201),s=r(307);e.exports={docx:function(){return{getTemplatedFiles:function(){return[]},textPath:function(e){return e.textTarget},tagsXmlTextArray:["Company","HyperlinkBase","Manager","cp:category","cp:keywords","dc:creator","dc:description","dc:subject","dc:title","cp:contentStatus","w:t","a:t","m:t","vt:lpstr","vt:lpwstr"],tagsXmlLexedArray:["w:proofState","w:tc","w:tr","w:tbl","w:body","w:document","w:p","w:r","w:br","w:rPr","w:pPr","w:spacing","w:sdtContent","w:sdt","w:drawing","w:sectPr","w:type","w:headerReference","w:footerReference","w:bookmarkStart","w:bookmarkEnd","w:commentRangeStart","w:commentRangeEnd","w:commentReference"],droppedTagsInsidePlaceholder:["w:p","w:br","w:bookmarkStart","w:bookmarkEnd"],expandTags:[{contains:"w:tc",expand:"w:tr"}],onParagraphLoop:[{contains:"w:p",expand:"w:p",onlyTextInTag:!0}],tagRawXml:"w:p",baseModules:[n,o,i,a,s],tagShouldContain:[{tag:"w:sdtContent",shouldContain:["w:p","w:r","w:commentRangeStart","w:sdt"],value:"<w:p></w:p>"},{tag:"w:tc",shouldContain:["w:p"],value:"<w:p></w:p>"},{tag:"w:tr",shouldContain:["w:tc"],drop:!0},{tag:"w:tbl",shouldContain:["w:tr"],drop:!0}]}},pptx:function(){return{getTemplatedFiles:function(){return[]},textPath:function(e){return e.textTarget},tagsXmlTextArray:["Company","HyperlinkBase","Manager","cp:category","cp:keywords","dc:creator","dc:description","dc:subject","dc:title","a:t","m:t","vt:lpstr","vt:lpwstr"],tagsXmlLexedArray:["p:sp","a:tc","a:tr","a:tbl","a:graphicData","a:p","a:r","a:rPr","p:txBody","a:txBody","a:off","a:ext","p:graphicFrame","p:xfrm","a16:rowId","a:endParaRPr"],droppedTagsInsidePlaceholder:["a:p","a:endParaRPr"],expandTags:[{contains:"a:tc",expand:"a:tr"}],onParagraphLoop:[{contains:"a:p",expand:"a:p",onlyTextInTag:!0}],tagRawXml:"p:sp",baseModules:[n,i,a,s],tagShouldContain:[{tag:"a:tbl",shouldContain:["a:tr"],dropParent:"p:graphicFrame"},{tag:"p:txBody",shouldContain:["a:p"],value:"<a:p></a:p>"},{tag:"a:txBody",shouldContain:["a:p"],value:"<a:p></a:p>"}]}}}},307:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,a(n.key),n)}}function a(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var i=r(899),s=r(946),l=s.getScopeCompilationError,u=s.getCorruptCharactersException,p=r(207),c=p.utf8ToWord,f=p.hasCorruptCharacters,h=p.removeCorruptCharacters,d=r(356),v=[d.settingsContentType,d.coreContentType,d.appContentType,d.customContentType],g={docx:"w",pptx:"a"},m=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="Render",this.recordRun=!1,this.recordedRun=[]},t=[{key:"optionsTransformer",value:function(e,t){return this.docxtemplater=t,this.brTag="docx"===t.fileType?"<w:r><w:br/></w:r>":"<a:br/>",this.prefix=g[t.fileType],this.runStartTag="".concat(this.prefix,":r"),this.runPropsStartTag="".concat(this.prefix,":rPr"),e}},{key:"set",value:function(e){e.compiled&&(this.compiled=e.compiled),null!=e.data&&(this.data=e.data)}},{key:"getRenderedMap",value:function(e){for(var t in this.compiled)e[t]={from:t,data:this.data};return e}},{key:"postparse",value:function(e,t){for(var r=[],n=0;n<e.length;n++){var o=e[n];if("placeholder"===o.type){var a=o.value;try{t.cachedParsers[o.lIndex]=this.docxtemplater.parser(a,{tag:o})}catch(e){r.push(l({tag:a,rootError:e,offset:o.offset}))}}}return{postparsed:e,errors:r}}},{key:"render",value:function(e,t){var r=t.contentType,n=t.scopeManager,o=t.linebreaks,a=t.nullGetter,i=t.fileType,s=t.stripInvalidXMLChars;if(-1!==v.indexOf(r)&&(o=!1),o&&this.recordRuns(e),"placeholder"===e.type&&!e.module){var l;try{l=n.getValue(e.value,{part:e})}catch(e){return{errors:[e]}}if(null!=l||(l=a(e)),"string"==typeof l)if(s)l=h(l);else if(-1!==["docx","pptx","xlsx"].indexOf(i)&&f(l))return{errors:[u({tag:e.value,value:l,offset:e.offset})]};return"text"===i?{value:l}:{value:o&&"string"==typeof l?this.renderLineBreaks(l):c(l)}}}},{key:"recordRuns",value:function(e){e.tag===this.runStartTag?this.recordedRun="":e.tag===this.runPropsStartTag?("start"===e.position&&(this.recordRun=!0,this.recordedRun+=e.value),"end"!==e.position&&"selfclosing"!==e.position||(this.recordedRun+=e.value,this.recordRun=!1)):this.recordRun&&(this.recordedRun+=e.value)}},{key:"renderLineBreaks",value:function(e){for(var t=[],r=e.split("\n"),n=0,o=r.length;n<o;n++)t.push(c(r[n])),n<r.length-1&&t.push("</".concat(this.prefix,":t></").concat(this.prefix,":r>").concat(this.brTag,"<").concat(this.prefix,":r>").concat(this.recordedRun,"<").concat(this.prefix,":t").concat("docx"===this.docxtemplater.fileType?' xml:space="preserve"':"",">"));return t}}],t&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return i(new m)}},320:function(e){e.exports={last:function(e){return e[e.length-1]},first:function(e){return e[0]}}},322:function(e){var t=["application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml","application/vnd.ms-word.document.macroEnabled.main+xml","application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml","application/vnd.ms-word.template.macroEnabledTemplate.main+xml"],r={main:t,docx:["application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml"].concat(t,["application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml","application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml","application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml"]),pptx:["application/vnd.openxmlformats-officedocument.presentationml.slide+xml","application/vnd.openxmlformats-officedocument.presentationml.slideMaster+xml","application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml","application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml"],xlsx:["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml","application/vnd.ms-excel.sheet.macroEnabled.main+xml","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"]};e.exports=r},356:function(e){e.exports={settingsContentType:"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml",coreContentType:"application/vnd.openxmlformats-package.core-properties+xml",appContentType:"application/vnd.openxmlformats-officedocument.extended-properties+xml",customContentType:"application/vnd.openxmlformats-officedocument.custom-properties+xml",diagramDataContentType:"application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml",diagramDrawingContentType:"application/vnd.ms-office.drawingml.diagramDrawing+xml"}},367:function(e,t,r){var n=r(207).pregMatchAll;e.exports=function(e,t){var r={content:e},o=t.join("|"),a=new RegExp("(?:(<(?:".concat(o,")[^>]*>)([^<>]*)</(?:").concat(o,")>)|(<(?:").concat(o,")[^>]*/>)"),"g");return r.matches=n(a,r.content),r}},438:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,a(n.key),n)}}function a(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var i=r(207).pushArray,s=r(899),l=r(322),u=r(356),p=[u.settingsContentType,u.coreContentType,u.appContentType,u.customContentType,u.diagramDataContentType,u.diagramDrawingContentType],c=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="Common"},(t=[{key:"getFileType",value:function(e){var t=e.doc,r=t.invertedContentTypes;if(r){for(var n=0;n<p.length;n++){var o=p[n];r[o]&&i(t.targets,r[o])}for(var a,s=["docx","pptx","xlsx"],u=0;u<s.length;u++){for(var c=s[u],f=l[c],h=0;h<f.length;h++){var d=f[h];if(r[d])for(var v=0,g=r[d];v<g.length;v++){var m=g[v];t.relsTypes[m]&&-1===["http://purl.oclc.org/ooxml/officeDocument/relationships/officeDocument","http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument"].indexOf(t.relsTypes[m])||(a=c,-1===l.main.indexOf(d)&&d!==l.pptx[0]||t.textTarget||(t.textTarget=m),"xlsx"!==a&&t.targets.push(m))}}if(a)return a}return a}}}])&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return s(new c)}},460:function(e,t,r){var n=r(207).pushArray;function o(e,t){return t instanceof Error?n(Object.getOwnPropertyNames(t),["stack"]).reduce((function(e,r){return e[r]=t[r],"stack"===r&&(e[r]=t[r].toString()),e}),{}):t}e.exports=function(e,t){if(console.log(JSON.stringify({error:e},o,"json"===t?2:null)),e.properties&&e.properties.errors instanceof Array){var r=e.properties.errors.map((function(e){return e.properties.explanation})).join("\n");console.log("errorMessages",r)}}},502:function(e,t,r){var n=r(207).str2xml;e.exports={getRelsTypes:function(e){for(var t=e.files["_rels/.rels"],r=t?n(t.asText()):null,o=r?r.getElementsByTagName("Relationship"):[],a={},i=0;i<o.length;i++){var s=o[i];a[s.getAttribute("Target")]=s.getAttribute("Type")}return a}}},522:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,a(n.key),n)}}function a(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var i=r(899),s=r(207),l=s.isTextStart,u=s.isTextEnd,p=s.endsWith,c=s.startsWith,f=s.pushArray;function h(e,t){var r=e[t].value;return"</w:t>"===e[t+1].value||-1!==r.indexOf('xml:space="preserve"')?r:r.substr(0,r.length-1)+' xml:space="preserve">'}var d=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="SpacePreserveModule"},(t=[{key:"postparse",value:function(e,t){var r=[],n=!1,o=0,a=0,i=e.reduce((function(e,i){return function(e){return l(e)&&"w:t"===e.tag}(i)&&(n=!0,a=r.length),n?(r.push(i),function(e,t){return e&&e.basePart&&t.length>1}(t,r)&&(o=t.basePart.endLindex,r[0].value=h(r,0)),function(e,t){return"placeholder"===e.type&&t.length>1}(i,r)&&(r[a].value=h(r,a),o=i.endLindex),u(i)&&i.lIndex>o&&(0!==o&&(r[a].value=h(r,a)),f(e,r),r=[],n=!1,o=0,a=0),e):(e.push(i),e)}),[]);return f(i,r),i}},{key:"postrender",value:function(e){for(var t="",r=0,n=0,o=e.length;n<o;n++){var a=e[n];""!==a&&(p(t,'<w:t xml:space="preserve">')&&c(a,"</w:t>")&&(e[r]=t.substr(0,t.length-26)+"<w:t/>",a=a.substr(6)),t=a,r=n,e[n]=a)}return e}}])&&o(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return i(new d)}},536:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],l=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return s}}(e,t)||a(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(e,t){if(e){if("string"==typeof e)return i(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?i(e,t):void 0}}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function u(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var p=r(207),c=p.getRightOrNull,f=p.getRight,h=p.getLeft,d=p.getLeftOrNull,v=p.chunkBy,g=p.isTagStart,m=p.isTagEnd,y=p.isContent,b=p.last,x=p.first,w=r(946),T=w.XTTemplateError,P=w.throwExpandNotFound,O=w.getLoopPositionProducesInvalidXMLError;function S(e,t){return 0!==e.length&&0===b(e).substr(1).indexOf(t)}function j(e,t,r,n){var o=e.expandTo||n.expandTo;if(o){var a,i;try{i=h(r,o,t),a=f(r,o,t)}catch(a){var s=l({part:e,rootError:a,postparsed:r,expandTo:o,index:t},n.error);if(n.onError&&"ignore"===n.onError(s))return;P(s)}return[i,a]}}function E(e,t,r,n){var a=o(e,2),i=a[0],s=a[1],l=r.indexOf(t),u=r.slice(i,l),p=r.slice(l+1,s+1),c=n.getInner({postparse:n.postparse,index:l,part:t,leftParts:u,rightParts:p,left:i,right:s,postparsed:r});return c.length||(c.expanded=[u,p],c=[c]),{left:i,right:s,inner:c}}e.exports={expandToOne:function(e,t){var r,n=[];e.errors&&(n=e.errors,e=e.postparsed);for(var s=[],u=0,p=e.length;u<p;u++){var c=e[u];if("placeholder"===c.type&&c.module===t.moduleName&&!c.subparsed&&!c.expanded)try{var f=j(c,u,e,t);if(!f)continue;var h=o(f,2),d=h[0],v=h[1];s.push({left:d,right:v,part:c,i:u,leftPart:e[d],rightPart:e[v]})}catch(e){n.push(e)}}s.sort((function(e,t){return e.left===t.left?t.part.lIndex<e.part.lIndex?1:-1:t.left<e.left?1:-1}));for(var g=-1,m=0,y=0,b=s.length;y<b;y++){var x,w=s[y];if(g=Math.max(g,y>0?s[y-1].right:0),!(w.left<g)){var P=void 0;try{P=E([w.left+m,w.right+m],w.part,e,t)}catch(r){if(t.onError&&"ignore"===t.onError(l({part:w.part,rootError:r,postparsed:e,expandOne:E},t.errors)))continue;if(!(r instanceof T))throw r;n.push(r)}P&&(m+=P.inner.length-(P.right+1-P.left),(x=e).splice.apply(x,[P.left,P.right+1-P.left].concat(function(e){if(Array.isArray(e))return i(e)}(r=P.inner)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(r)||a(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())))}}return{postparsed:e,errors:n}},getExpandToDefault:function(e,t,r){var n=function(e){for(var t=[],r=0;r<e.length;r++){var n=e[r],o=n.position,a=n.value,i=n.tag;i&&("end"===o?S(t,i)?t.pop():t.push(a):"start"===o&&t.push(a))}return t}(e.slice(t[0].offset,t[1].offset));if(n.filter((function(e){return"/"===e[1]})).length!==n.filter((function(e){return"/"!==e[1]&&"/"!==e[e.length-2]})).length)return{error:O({tag:x(t).part.value,offset:[x(t).part.offset,b(t).part.offset]})};for(var o,a=function(){var o=r[i],a=o.contains,s=o.expand,l=o.onlyTextInTag;if(function(e,t){for(var r=0;r<t.length;r++)if(0===t[r].indexOf("<".concat(e)))return!0;return!1}(a,n)){if(l){var u=d(e,a,t[0].offset),p=c(e,a,t[1].offset);if(null===u||null===p)return 0;var f=v(e.slice(u,p),(function(e){return g(a,e)?"start":m(a,e)?"end":null})),h=x(f),w=b(f),T=h.filter(y),P=w.filter(y);if(1!==T.length||1!==P.length)return 0}return{v:{value:s}}}},i=0;i<r.length;i++)if(0!==(o=a())&&o)return o.v;return{}}}},650:function(e){function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}var r=new RegExp(String.fromCharCode(160),"g");function n(e){return e.replace(r," ")}e.exports={match:function(e,r){var o=t(e);return"string"===o?n(r.substr(0,e.length))===e:e instanceof RegExp?e.test(n(r)):"function"===o?!!e(r):void 0},getValue:function(e,r){var o=t(e);return"string"===o?n(r).substr(e.length):e instanceof RegExp?n(r).match(e)[1]:"function"===o?e(r):void 0},getValues:function(e,r){var o=t(e);return"string"===o?[r,n(r).substr(e.length)]:e instanceof RegExp?n(r).match(e):"function"===o?[r,e(r)]:void 0}}},673:function(e){e.exports={XMLSerializer:window.XMLSerializer,DOMParser:window.DOMParser,XMLDocument:window.XMLDocument}},690:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],l=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return l(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var u=r(207),p=u.wordToUtf8,c=u.pushArray,f=r(650),h=f.match,d=f.getValue,v=f.getValues;var g={preparse:function(e,t,r){return function(e,r){for(var n=0;n<t.length;n++)e=t[n].preparse(e,r)||e;return e}(e,r)},parse:function(e,t,r){var n,o=!1,i="",l=[],u=r.fileTypeConfig.droppedTagsInsidePlaceholder||[];return e.reduce((function(e,f){return"delimiter"===f.type?(o="start"===f.position,"end"===f.position&&(r.parse=function(e){return function(e,t){var r,n=t.modules,o=t.startOffset,i=t.lIndex;t.offset=o,t.match=h,t.getValue=d,t.getValues=v;var l=function(e,t){for(var r=[],n=0;n<e.length;n++){var o=e[n];if(o.matchers){var a=o.matchers(t);if(!(a instanceof Array))throw new Error("module matcher returns a non array");c(r,a)}}return r}(n,t),u=function(e,t,r){for(var n=[],o=0;o<e.length;o++){var i=e[o],l=s(i,2),u=l[0],p=l[1],c=i[2]||{};if(r.match(u,t)){var f=r.getValues(u,t);if("function"==typeof c&&(c=c(f)),!c.value){var h=s(f,2);c.value=h[1]}n.push(a({type:"placeholder",prefix:u,module:p,onMatch:c.onMatch,priority:c.priority},c))}}return n}(l,e,t);if(u.length>0){for(var p=null,f=0;f<u.length;f++){var g=u[f];g.priority||(g.priority=-g.value.length),(!p||g.priority>p.priority)&&(p=g)}return p.offset=o,delete p.priority,p.endLindex=i,p.lIndex=i,p.raw=e,p.onMatch&&p.onMatch(p),delete p.onMatch,delete p.prefix,p}for(var m=0;m<n.length;m++)if(r=n[m].parse(e,t))return r.offset=o,r.endLindex=i,r.lIndex=i,r.raw=e,r;return{type:"placeholder",value:e,offset:o,endLindex:i,lIndex:i}}(e,a(a(a({},r),f),{},{startOffset:n,modules:t}))},e.push(r.parse(p(i))),c(e,l),l=[]),"start"===f.position&&(l=[],n=f.offset),i="",e):o?"content"!==f.type||"insidetag"!==f.position?(-1!==u.indexOf(f.tag)||l.push(f),e):(i+=f.value,e):(e.push(f),e)}),[])},postparse:function(e,t,r){function n(e,r){return t.map((function(t){return t.getTraits(e,r)}))}var o=[];return{postparsed:function e(r,i){for(var s=r,l=0;l<t.length;l++){var u=t[l].postparse(s,a(a({},i),{},{postparse:function(t,r){return e(t,a(a({},i),r))},getTraits:n}));null!=u&&(u.errors?(c(o,u.errors),s=u.postparsed):s=u)}return s}(e,r),errors:o}}};e.exports=g},779:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,a(n.key),n)}}function a(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var i=r(946).getScopeParserExecutionError,s=r(320).last,l=r(207).concatArrays;function u(e,t){for(var r,n=e.length>>>0,o=0;o<n;o++)if(r=e[o],t.call(this,r,o,e))return r}function p(e,t,r){var n,o,a=this,s=this.scopeList[r];if(this.root.finishedResolving){for(var l=this.resolved,c=function(){var e=a.scopeLindex[f];l=(l=u(l,(function(t){return t.lIndex===e}))).value[a.scopePathItem[f]]},f=this.resolveOffset,h=this.scopePath.length;f<h;f++)c();return u(l,(function(e){return t.part.lIndex===e.lIndex})).value}o=this.cachedParsers&&t.part?this.cachedParsers[t.part.lIndex]?this.cachedParsers[t.part.lIndex]:this.cachedParsers[t.part.lIndex]=this.parser(e,{tag:t.part,scopePath:this.scopePath}):this.parser(e,{tag:t.part,scopePath:this.scopePath});try{n=o.get(s,this.getContext(t,r))}catch(r){throw i({tag:e,scope:s,error:r,offset:t.part.offset})}return null==n&&r>0?p.call(this,e,t,r-1):n}function c(e,t,r){var n,o=this,a=this.scopeList[r];return n=this.cachedParsers&&t.part?this.cachedParsers[t.part.lIndex]?this.cachedParsers[t.part.lIndex]:this.cachedParsers[t.part.lIndex]=this.parser(e,{tag:t.part,scopePath:this.scopePath}):this.parser(e,{tag:t.part,scopePath:this.scopePath}),Promise.resolve().then((function(){return n.get(a,o.getContext(t,r))})).catch((function(r){throw i({tag:e,scope:a,error:r,offset:t.part.offset})})).then((function(n){return null==n&&r>0?c.call(o,e,t,r-1):n}))}var f=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.root=t.root||this,this.resolveOffset=t.resolveOffset||0,this.scopePath=t.scopePath,this.scopePathItem=t.scopePathItem,this.scopePathLength=t.scopePathLength,this.scopeList=t.scopeList,this.scopeType="",this.scopeTypes=t.scopeTypes,this.scopeLindex=t.scopeLindex,this.parser=t.parser,this.resolved=t.resolved,this.cachedParsers=t.cachedParsers}return t=e,(r=[{key:"loopOver",value:function(e,t,r,n){return this.loopOverValue(this.getValue(e,n),t,r)}},{key:"functorIfInverted",value:function(e,t,r,n,o){return e&&t(r,n,o),e}},{key:"isValueFalsy",value:function(e,t){return null==e||!e||"[object Array]"===t&&0===e.length}},{key:"loopOverValue",value:function(e,t,r){this.root.finishedResolving&&(r=!1);var n=Object.prototype.toString.call(e);if(this.isValueFalsy(e,n))return this.scopeType=!1,this.functorIfInverted(r,t,s(this.scopeList),0,1);if("[object Array]"===n){this.scopeType="array";for(var o=0;o<e.length;o++)this.functorIfInverted(!r,t,e[o],o,e.length);return!0}return"[object Object]"===n?(this.scopeType="object",this.functorIfInverted(!r,t,e,0,1)):this.functorIfInverted(!r,t,s(this.scopeList),0,1)}},{key:"getValue",value:function(e,t){var r=p.call(this,e,t,this.scopeList.length-1);return"function"==typeof r?r(this.scopeList[this.scopeList.length-1],this):r}},{key:"getValueAsync",value:function(e,t){var r=this;return c.call(this,e,t,this.scopeList.length-1).then((function(e){return"function"==typeof e?e(r.scopeList[r.scopeList.length-1],r):e}))}},{key:"getContext",value:function(e,t){return{num:t,meta:e,scopeList:this.scopeList,resolved:this.resolved,scopePath:this.scopePath,scopeTypes:this.scopeTypes,scopePathItem:this.scopePathItem,scopePathLength:this.scopePathLength}}},{key:"createSubScopeManager",value:function(t,r,n,o,a){return new e({root:this.root,resolveOffset:this.resolveOffset,resolved:this.resolved,parser:this.parser,cachedParsers:this.cachedParsers,scopeTypes:l([this.scopeTypes,[this.scopeType]]),scopeList:l([this.scopeList,[t]]),scopePath:l([this.scopePath,[r]]),scopePathItem:l([this.scopePathItem,[n]]),scopePathLength:l([this.scopePathLength,[a]]),scopeLindex:l([this.scopeLindex,[o.lIndex]])})}}])&&o(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();e.exports=function(e){return e.scopePath=[],e.scopePathItem=[],e.scopePathLength=[],e.scopeTypes=[],e.scopeLindex=[],e.scopeList=[e.tags],new f(e)}},789:function(e,t,r){var n=r(946),o=n.throwUnimplementedTagType,a=n.XTScopeParserError,i=r(207).pushArray,s=r(830);function l(e,t){for(var r=0,n=t.modules;r<n.length;r++){var o=n[r].render(e,t);if(o)return o}return!1}e.exports=function(e){var t=e.baseNullGetter,r=e.compiled,n=e.scopeManager;e.nullGetter=function(e,r){return t(e,r||n)};for(var u=[],p=[],c=0,f=r.length;c<f;c++){var h=r[c];e.index=c,e.resolvedId=s(h,e);var d=void 0;try{d=l(h,e)}catch(e){if(e instanceof a){u.push(e),p.push(h);continue}throw e}d?(d.errors&&i(u,d.errors),p.push(d)):"content"!==h.type&&"tag"!==h.type?o(h,c):p.push(h)}for(var v=[],g=0;g<p.length;g++){var m=p[g].value;m instanceof Array?i(v,m):m&&v.push(m)}return{errors:u,parts:v}}},798:function(e){function t(e,t){for(var r=-1,n=0,o=e.length;n<o;n++)t[n]>=e[n].length||(-1===r||e[n][t[n]].offset<e[r][t[r]].offset)&&(r=n);return r}e.exports=function(e){for(var r=0,n=0,o=e;n<o.length;n++)r+=o[n].length;e=e.filter((function(e){return e.length>0}));for(var a=new Array(r),i=e.map((function(){return 0})),s=0;s<r;s++){var l=t(e,i);a[s]=e[l][i[l]],i[l]++}return a}},807:function(e,t,r){var n=["modules"];function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e,t,r){return(t=c(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],l=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return l(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function u(e){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},u(e)}function p(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,c(n.key),n)}}function c(e){var t=function(e){if("object"!=u(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=u(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==u(t)?t:t+""}var f=r(207),h=r(502).getRelsTypes,d=r(181),v=d.collectContentTypes,g=d.getContentTypes,m=r(899),y=r(536),b=r(438),x=r(779),w=r(263),T=r(903).getTags,P=r(460),O=r(946),S=O.throwMultiError,j=O.throwResolveBeforeCompile,E=O.throwRenderInvalidTemplate,C=O.throwRenderTwice,k=O.XTInternalError,I=O.XTTemplateError,A=O.throwFileTypeNotIdentified,M=O.throwFileTypeNotHandled,D=O.throwApiVersionError;f.getRelsTypes=h,f.traits=y,f.moduleWrapper=m,f.collectContentTypes=v,f.getContentTypes=g;var L=f.getDefaults,_=f.str2xml,R=f.xml2str,F=f.concatArrays,X=f.uniq,V=f.getDuplicates,z=f.stableSort,N=f.pushArray,U=f.utf8ToWord,B=f.invertMap,G=[3,47,0];function W(e){var t=[];for(var r in e)t.push(r);for(var n=["[Content_Types].xml","_rels/.rels"],o=["word/","xl/","ppt/"],a=0;a<t.length;a++)for(var i=t[a],s=0;s<o.length;s++){var l=o[s];0===i.indexOf("".concat(l))&&n.push(i)}for(var u=0;u<t.length;u++){var p=t[u];-1===n.indexOf(p)&&n.push(p)}return n}function q(e,t){!0!==e.hideDeprecations&&console.warn(t)}function Z(e,t){if(!0!==e.hideDeprecations)return q(e,'Deprecated method ".'.concat(t,'", view upgrade guide : https://docxtemplater.com/docs/api/#upgrade-guide, stack : ').concat((new Error).stack))}function $(e){e.modules=e.modules.filter((function(t){if(!t.supportedFileTypes)return!0;if(!Array.isArray(t.supportedFileTypes))throw new Error("The supportedFileTypes field of the module must be an array");var r=t.supportedFileTypes.includes(e.fileType);return r||t.on("detached"),r}))}function Y(e){var t=e.compiled;e.errors=F(Object.keys(t).map((function(e){return t[e].allErrors}))),0!==e.errors.length&&(e.options.errorLogging&&P(e.errors,e.options.errorLogging),S(e.errors))}var H=function(){function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.modules,a=void 0===o?[]:o,i=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if({}.hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&{}.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(r,n);if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.targets=[],this.rendered=!1,this.scopeManagers={},this.compiled={},this.modules=[b()],this.xmlDocuments={},0===arguments.length)q(this,"Deprecated docxtemplater constructor with no arguments, view upgrade guide : https://docxtemplater.com/docs/api/#upgrade-guide, stack : ".concat((new Error).stack)),this.hideDeprecations=!0,this.setOptions(i);else{if(this.hideDeprecations=!0,this.setOptions(i),!t||!t.files||"function"!=typeof t.file)throw new Error("The first argument of docxtemplater's constructor must be a valid zip file (jszip v2 or pizzip v3)");if(!Array.isArray(a))throw new Error("The modules argument of docxtemplater's constructor must be an array");for(var s=0;s<a.length;s++){var l=a[s];this.attachModule(l)}this.loadZip(t),this.compile(),this.v4Constructor=!0}this.hideDeprecations=!1}return t=e,r=[{key:"verifyApiVersion",value:function(e){return 3!==(e=e.split(".").map((function(e){return parseInt(e,10)}))).length&&D("neededVersion is not a valid version",{neededVersion:e,explanation:"the neededVersion must be an array of length 3"}),e[0]!==G[0]&&D("The major api version do not match, you probably have to update docxtemplater with npm install --save docxtemplater",{neededVersion:e,currentModuleApiVersion:G,explanation:"moduleAPIVersionMismatch : needed=".concat(e.join("."),", current=").concat(G.join("."))}),e[1]>G[1]&&D("The minor api version is not uptodate, you probably have to update docxtemplater with npm install --save docxtemplater",{neededVersion:e,currentModuleApiVersion:G,explanation:"moduleAPIVersionMismatch : needed=".concat(e.join("."),", current=").concat(G.join("."))}),e[1]===G[1]&&e[2]>G[2]&&D("The patch api version is not uptodate, you probably have to update docxtemplater with npm install --save docxtemplater",{neededVersion:e,currentModuleApiVersion:G,explanation:"moduleAPIVersionMismatch : needed=".concat(e.join("."),", current=").concat(G.join("."))}),!0}},{key:"setModules",value:function(e){for(var t=0,r=this.modules;t<r.length;t++)r[t].set(e)}},{key:"sendEvent",value:function(e){for(var t=0,r=this.modules;t<r.length;t++)r[t].on(e)}},{key:"attachModule",value:function(e){if(this.v4Constructor)throw new k("attachModule() should not be called manually when using the v4 constructor");Z(this,"attachModule");var t=u(e);if("function"===t)throw new k("Cannot attach a class/function as a module. Most probably you forgot to instantiate the module by using `new` on the module.");if(!e||"object"!==t)throw new k("Cannot attachModule with a falsy value");if(e.requiredAPIVersion&&this.verifyApiVersion(e.requiredAPIVersion),!0===e.attached){if("function"!=typeof e.clone)throw new Error('Cannot attach a module that was already attached : "'.concat(e.name,'". The most likely cause is that you are instantiating the module at the root level, and using it for multiple instances of Docxtemplater'));e=e.clone()}e.attached=!0;var r=m(e);return this.modules.push(r),r.on("attached"),this.fileType&&$(this),this}},{key:"setOptions",value:function(e){var t,r;if(this.v4Constructor)throw new Error("setOptions() should not be called manually when using the v4 constructor");if(Z(this,"setOptions"),!e)throw new Error("setOptions should be called with an object as first parameter");this.options={};var n=L();for(var o in n){var a=n[o];this.options[o]=null!=e[o]?e[o]:this[o]||a,this[o]=this.options[o]}return(t=this.delimiters).start&&(t.start=U(this.delimiters.start)),(r=this.delimiters).end&&(r.end=U(this.delimiters.end)),this}},{key:"loadZip",value:function(e){if(this.v4Constructor)throw new Error("loadZip() should not be called manually when using the v4 constructor");if(Z(this,"loadZip"),e.loadAsync)throw new k("Docxtemplater doesn't handle JSZip version >=3, please use pizzip");this.zip=e,this.updateFileTypeConfig(),this.modules=F([this.fileTypeConfig.baseModules.map((function(e){return e()})),this.modules]);for(var t=0,r=this.modules;t<r.length;t++){var n=r[t];n.zip=this.zip,n.docxtemplater=this}return $(this),this}},{key:"precompileFile",value:function(e){var t=this.createTemplateClass(e);t.preparse(),this.compiled[e]=t}},{key:"compileFile",value:function(e){this.compiled[e].parse()}},{key:"getScopeManager",value:function(e,t,r){var n;return(n=this.scopeManagers)[e]||(n[e]=x({tags:r,parser:this.parser,cachedParsers:t.cachedParsers})),this.scopeManagers[e]}},{key:"resolveData",value:function(e){var t=this;Z(this,"resolveData");var r=[];return Object.keys(this.compiled).length||j(),Promise.resolve(e).then((function(e){return t.data=e,t.setModules({data:t.data,Lexer:w}),t.mapper=t.modules.reduce((function(e,t){return t.getRenderedMap(e)}),{}),Promise.all(Object.keys(t.mapper).map((function(e){var n=t.mapper[e],o=n.from,a=n.data;return Promise.resolve(a).then((function(n){var a=t.compiled[o];return a.filePath=e,a.scopeManager=t.getScopeManager(e,a,n),a.resolveTags(n).then((function(e){return a.scopeManager.finishedResolving=!0,e}),(function(e){N(r,e)}))}))}))).then((function(e){return 0!==r.length&&(t.options.errorLogging&&P(r,t.options.errorLogging),S(r)),F(e)}))}))}},{key:"compile",value:function(){var e;if(Z(this,"compile"),this.updateFileTypeConfig(),function(e){var t=V(e.map((function(e){return e.name})));if(t.length>0)throw new k('Detected duplicate module "'.concat(t[0],'"'))}(this.modules),this.modules=(e=this.modules,z(e,(function(e,t){return(t.priority||0)-(e.priority||0)}))),Object.keys(this.compiled).length)return this;for(var t=this.options,r=0,n=this.modules;r<n.length;r++)t=n[r].optionsTransformer(t,this);this.options=t,this.options.xmlFileNames=X(this.options.xmlFileNames);for(var o=0,a=this.options.xmlFileNames;o<a.length;o++){var i=a[o],s=this.zip.files[i].asText();this.xmlDocuments[i]=_(s)}this.setModules({zip:this.zip,xmlDocuments:this.xmlDocuments}),this.getTemplatedFiles(),this.sendEvent("before-preparse");for(var l=0,u=this.templatedFiles;l<u.length;l++){var p=u[l];null!=this.zip.files[p]&&this.precompileFile(p)}this.sendEvent("after-preparse");for(var c=0,f=this.templatedFiles;c<f.length;c++){var h=f[c];null!=this.zip.files[h]&&this.compiled[h].parse({noPostParse:!0})}this.sendEvent("after-parse");for(var d=0,v=this.templatedFiles;d<v.length;d++){var g=v[d];null!=this.zip.files[g]&&this.compiled[g].postparse()}return this.sendEvent("after-postparse"),this.setModules({compiled:this.compiled}),Y(this),this}},{key:"updateFileTypeConfig",value:function(){this.relsTypes=h(this.zip);var t,r=g(this.zip),n=r.overrides,o=r.defaults,a=r.contentTypes,i=r.contentTypeXml;i&&(this.filesContentTypes=v(n,o,this.zip),this.invertedContentTypes=B(this.filesContentTypes),this.setModules({contentTypes:this.contentTypes,invertedContentTypes:this.invertedContentTypes})),this.zip.files.mimetype&&(t="odt");for(var s=0,l=this.modules;s<l.length;s++)t=l[s].getFileType({zip:this.zip,contentTypes:a,contentTypeXml:i,overrides:n,defaults:o,doc:this})||t;if(this.fileType=t,"odt"===t&&M(t),t||A(this.zip),function(e){for(var t=0,r=e.modules;t<r.length;t++)for(var n=0,o=r[t].xmlContentTypes||[];n<o.length;n++)for(var a=o[n],i=e.invertedContentTypes[a]||[],s=0;s<i.length;s++){var l=i[s];e.zip.files[l]&&e.options.xmlFileNames.push(l)}}(this),$(this),this.fileTypeConfig=this.options.fileTypeConfig||this.fileTypeConfig,!this.fileTypeConfig){if(!e.FileTypeConfig[this.fileType]){var u='Filetype "'.concat(this.fileType,'" is not supported'),p="filetype_not_supported";"xlsx"===this.fileType&&(u='Filetype "'.concat(this.fileType,'" is supported only with the paid XlsxModule'),p="xlsx_filetype_needs_xlsx_module");var c=new I(u);throw c.properties={id:p,explanation:u},c}this.fileTypeConfig=e.FileTypeConfig[this.fileType]()}return this}},{key:"renderAsync",value:function(e){var t=this;this.hideDeprecations=!0;var r=this.resolveData(e);return this.hideDeprecations=!1,r.then((function(){return t.render()}))}},{key:"render",value:function(e){this.rendered&&C(),this.rendered=!0,0===Object.keys(this.compiled).length&&this.compile(),this.errors.length>0&&E(),arguments.length>0&&(this.data=e),this.setModules({data:this.data,Lexer:w}),this.mapper||(this.mapper=this.modules.reduce((function(e,t){return t.getRenderedMap(e)}),{}));var t=[];for(var r in this.mapper){var n=this.mapper[r],o=n.from,a=n.data,i=this.compiled[o];i.scopeManager=this.getScopeManager(r,i,a),i.render(r),t.push([r,i.content,i]),delete i.content}for(var l=0;l<t.length;l++)for(var u=t[l],p=s(u,3),c=p[1],f=p[2],h=0,d=this.modules;h<d.length;h++){var v=d[h];if(v.preZip){var g=v.preZip(c,f);"string"==typeof g&&(u[1]=g)}}for(var m=0;m<t.length;m++){var y=s(t[m],2),b=y[0],x=y[1];this.zip.file(b,x,{createFolders:!0})}return Y(this),this.sendEvent("syncing-zip"),this.syncZip(),this.sendEvent("synced-zip"),this}},{key:"syncZip",value:function(){for(var e in this.xmlDocuments){this.zip.remove(e);var t=R(this.xmlDocuments[e]);this.zip.file(e,t,{createFolders:!0})}}},{key:"setData",value:function(e){return Z(this,"setData"),this.data=e,this}},{key:"getZip",value:function(){return this.zip}},{key:"createTemplateClass",value:function(e){var t=this.zip.files[e].asText();return this.createTemplateClassFromContent(t,e)}},{key:"createTemplateClassFromContent",value:function(t,r){for(var n={filePath:r,contentType:this.filesContentTypes[r],relsType:this.relsTypes[r]},o=L(),a=N(Object.keys(o),["filesContentTypes","fileTypeConfig","fileType","modules"]),i=0;i<a.length;i++){var s=a[i];n[s]=this[s]}return new e.XmlTemplater(t,n)}},{key:"getFullText",value:function(e){return this.createTemplateClass(e||this.fileTypeConfig.textPath(this)).getFullText()}},{key:"getTemplatedFiles",value:function(){return this.templatedFiles=this.fileTypeConfig.getTemplatedFiles(this.zip),N(this.templatedFiles,this.targets),this.templatedFiles=X(this.templatedFiles),this.templatedFiles}},{key:"getTags",value:function(){var e={headers:[],footers:[]};for(var t in this.compiled){var r=this.filesContentTypes[t];"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"===r&&(e.document={target:t,tags:T(this.compiled[t].postparsed)}),"application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml"===r&&e.headers.push({target:t,tags:T(this.compiled[t].postparsed)}),"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml"===r&&e.footers.push({target:t,tags:T(this.compiled[t].postparsed)})}return e}},{key:"toBuffer",value:function(e){return this.zip.generate(a(a({compression:"DEFLATE",fileOrder:W},e),{},{type:"nodebuffer"}))}},{key:"toBlob",value:function(e){return this.zip.generate(a(a({compression:"DEFLATE",fileOrder:W},e),{},{type:"blob"}))}},{key:"toBase64",value:function(e){return this.zip.generate(a(a({compression:"DEFLATE",fileOrder:W},e),{},{type:"base64"}))}},{key:"toUint8Array",value:function(e){return this.zip.generate(a(a({compression:"DEFLATE",fileOrder:W},e),{},{type:"uint8array"}))}},{key:"toArrayBuffer",value:function(e){return this.zip.generate(a(a({compression:"DEFLATE",fileOrder:W},e),{},{type:"arraybuffer"}))}}],r&&p(t.prototype,r),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,r}();H.DocUtils=f,H.Errors=r(946),H.XmlTemplater=r(245),H.FileTypeConfig=r(271),H.XmlMatcher=r(367),e.exports=H,e.exports.default=H},830:function(e){e.exports=function(e,t){if(null==e.lIndex)return null;var r=t.scopeManager.scopePathItem;return e.parentPart&&(r=r.slice(0,r.length-1)),t.filePath+"@"+e.lIndex.toString()+"-"+r.join("-")}},885:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e,t,r){return(t=u(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function l(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,u(n.key),n)}}function u(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}var p=r(207),c=p.chunkBy,f=p.last,h=p.isParagraphStart,d=p.isModule,v=p.pushArray,g=p.isParagraphEnd,m=p.isContent,y=p.startsWith,b=p.isTagEnd,x=p.isTagStart,w=p.getSingleAttribute,T=p.setSingleAttribute,P=r(322),O=r(899),S="loop";function j(e){var t=function(e){for(var t=0;t<e.length;t++){var r=e[t];if("content"!==r.type)return r}return null}(e.subparsed);return null!=t&&"w:t"!==t.tag}function E(e){return e.hasPageBreak&&j(e)?'<w:p><w:r><w:br w:type="page"/></w:r></w:p>':""}function C(e){return e.some((function(e){return m(e)}))?0:e.length}function k(e){var t=e.parts.length-1;"</w:p>"===e.parts[t]?e.parts.splice(t,0,'<w:r><w:br w:type="page"/></w:r>'):e.parts.push('<w:p><w:r><w:br w:type="page"/></w:r></w:p>')}function I(e){return e.some((function(e){return"w:br"===e.tag&&-1!==e.value.indexOf('w:type="page"')}))}function A(e){return e.some((function(e){return"w:drawing"===e.tag}))}var M=function(){return e=function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.name="LoopModule",this.inXfrm=!1,this.totalSectPr=0,this.prefix={start:"#",end:"/",dash:/^-([^\s]+)\s(.+)/,inverted:"^"}},t=[{key:"optionsTransformer",value:function(e,t){return this.docxtemplater=t,e}},{key:"preparse",value:function(e,t){var r=t.contentType;-1!==P.main.indexOf(r)&&(this.sects=function(e){for(var t=!1,r=[],n=0;n<e.length;n++){var o=e[n];x("w:sectPr",o)&&(r.push([]),t=!0),t&&r[r.length-1].push(o),b("w:sectPr",o)&&(t=!1)}return r}(e))}},{key:"matchers",value:function(){var e=S;return[[this.prefix.start,e,{expandTo:"auto",location:"start",inverted:!1}],[this.prefix.inverted,e,{expandTo:"auto",location:"start",inverted:!0}],[this.prefix.end,e,{location:"end"}],[this.prefix.dash,e,function(e){var t=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,s=[],l=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?s(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(e,3);return{location:"start",inverted:!1,expandTo:t[1],value:t[2]}}]]}},{key:"getTraits",value:function(e,t){if("expandPair"===e){for(var r=[],n=0,o=t.length;n<o;n++){var a=t[n];d(a,S)&&null==a.subparsed&&r.push({part:a,offset:n})}return r}}},{key:"postparse",value:function(e,t){var r=t.basePart;if(r&&"docx"===this.docxtemplater.fileType&&e.length>0){r.sectPrCount=function(e){for(var t=!1,r=0,n=0;n<e.length;n++){var o=e[n];x("w:sectPr",o)&&(t=!0),t&&("w:headerReference"!==o.tag&&"w:footerReference"!==o.tag||(r++,t=!1)),b("w:sectPr",o)&&(t=!1)}return r}(e),this.totalSectPr+=r.sectPrCount;var n=this.sects;n.some((function(t,o){return r.lIndex<t[0].lIndex?(o+1<n.length&&n[o+1].some((function(e){return x("w:type",e)&&-1!==e.value.indexOf("continuous")}))&&(r.addContinuousType=!0),!0):e[0].lIndex<t[0].lIndex&&t[0].lIndex<r.lIndex?(n[o].some((function(e){return x("w:type",e)&&-1!==e.value.indexOf('w:val="nextPage"')}))&&(r.addNextPage={index:o}),!0):void 0})),r.lastParagrapSectPr=function(e){for(var t=[],r=!1,n=e.length-1;n>=0;n--){var o=e[n];if(b("w:sectPr",o)&&(r=!0),x("w:sectPr",o)&&(t.unshift(o.value),r=!1),r&&t.unshift(o.value),h(o)){if(t.length>0)return t.join("");break}}return""}(e)}if(!r||"auto"!==r.expandTo||r.module!==S||!function(e){return e.length&&h(e[0])&&g(f(e))}(e))return e;r.paragraphLoop=!0;var o=0,a=c(e,(function(e){return h(e)&&1==++o?"start":g(e)&&0==--o?"end":null})),i=a[0],s=f(a),l=C(i),u=C(s);return r.hasPageBreakBeginning=I(i),r.hasPageBreak=I(s),A(i)&&(l=0),A(s)&&(u=0),e.slice(l,e.length-u)}},{key:"resolve",value:function(e,t){if(!d(e,S))return null;var r=t.scopeManager,n=r.getValueAsync(e.value,{part:e}),o=[];function i(n,i,s){var l=r.createSubScopeManager(n,e.value,i,e,s);o.push(t.resolve(a(a({},t),{},{compiled:e.subparsed,tags:{},scopeManager:l})))}var s=[];return n.then((function(n){return null!=n||(n=t.nullGetter(e)),new Promise((function(e){if(n instanceof Promise)return n.then((function(t){t instanceof Array?Promise.all(t).then(e):e(t)}));n instanceof Array?Promise.all(n).then(e):e(n)})).then((function(t){return r.loopOverValue(t,i,e.inverted),Promise.all(o).then((function(e){return e.map((function(e){var t=e.resolved,r=e.errors;return v(s,r),t}))})).then((function(e){if(s.length>0)throw s;return e}))}))}))}},{key:"render",value:function(e,t){if("p:xfrm"===e.tag&&(this.inXfrm="start"===e.position),"a:ext"===e.tag&&this.inXfrm)return this.lastExt=e,e;if(!d(e,S))return null;var r=[],n=[],o=0,i=this,s=e.subparsed[0],l=0;"a:tr"===(null==s?void 0:s.tag)&&(l=+w(s.value,"h")),o-=l;var u=0,p=j(e),c=t.scopeManager.getValue(e.value,{part:e});if(null!=c||(c=t.nullGetter(e)),!1===t.scopeManager.loopOverValue(c,(function(s,c,f){o+=l;for(var h=t.scopeManager.createSubScopeManager(s,e.value,c,e,f),d=0,g=e.subparsed;d<g.length;d++){var m=g[d];if(x("a16:rowId",m)){var b=+w(m.value,"val")+u;u=1,m.value=T(m.value,"val",b)}}var P,O,S=t.render(a(a({},t),{},{compiled:e.subparsed,tags:{},scopeManager:h}));e.hasPageBreak&&c===f-1&&p&&k(S),h.scopePathItem.some((function(e){return 0!==e}))?(1===e.sectPrCount&&(S.parts=S.parts.filter((function(e){return!y(e,"<w:headerReference")&&!y(e,"<w:footerReference")}))),e.addContinuousType&&(S.parts=function(e){for(var t=!1,r=!1,n=[],o=0;o<e.length;o++){var a=e[o];!1===t&&y(a,"<w:sectPr")&&(r=!0),r&&(y(a,"<w:type")&&(t=!0),!1===t&&y(a,"</w:sectPr")&&n.push('<w:type w:val="continuous"/>')),n.push(a)}return n}(S.parts))):e.addNextPage&&(S.parts=(P=S.parts,O=i.sects[e.addNextPage.index],v(["<w:p><w:pPr>".concat(O.map((function(e){return e.value})).join(""),"</w:pPr></w:p>")],P))),e.addNextPage&&k(S),e.hasPageBreakBeginning&&p&&function(e){e.parts.unshift('<w:p><w:r><w:br w:type="page"/></w:r></w:p>')}(S);for(var j=0,E=S.parts;j<E.length;j++){var C=E[j];r.push(C)}v(n,S.errors)}),e.inverted))return e.lastParagrapSectPr?e.paragraphLoop?{value:"<w:p><w:pPr>".concat(e.lastParagrapSectPr,"</w:pPr></w:p>")}:{value:"</w:t></w:r></w:p><w:p><w:pPr>".concat(e.lastParagrapSectPr,"</w:pPr><w:r><w:t>")}:{value:E(e)||"",errors:n};if(0!==o){var f=+w(this.lastExt.value,"cy");this.lastExt.value=T(this.lastExt.value,"cy",f+o)}return{value:t.joinUncorrupt(r,a(a({},t),{},{basePart:e})),errors:n}}}],t&&l(e.prototype,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,t}();e.exports=function(){return O(new M)}},899:function(e,t,r){var n=r(946).XTInternalError;function o(){}function a(e){return e}e.exports=function(e){var t={set:o,matchers:function(){return[]},parse:o,render:o,getTraits:o,getFileType:o,nullGetter:o,optionsTransformer:a,postrender:a,errorsTransformer:a,getRenderedMap:a,preparse:a,postparse:a,on:o,resolve:o,preResolve:o};if(Object.keys(t).every((function(t){return!e[t]}))){var r=new n("This module cannot be wrapped, because it doesn't define any of the necessary functions");throw r.properties={id:"module_cannot_be_wrapped",explanation:"This module cannot be wrapped, because it doesn't define any of the necessary functions"},r}for(var i in t)e[i]||(e[i]=t[i]);return e}},903:function(e){function t(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return r(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function n(e){return"placeholder"===e.type}e.exports={getTags:function(e){var r={},o=[{items:e.filter(n),parents:[],path:[]}];function a(e,r,n){n.length&&o.push({items:n,parents:[].concat(t(r.parents),[e]),path:!1===e.dataBound||e.attrParsed||!e.value||e.attrParsed?t(r.path):[].concat(t(r.path),[e.value])})}function i(e,t){for(var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.length,n=e,o=0;o<r;o++)n=n[t[o]];return n}function s(e,t){for(var r=t.length,n=0;n<t.length;n++){var o=t[n];("number"==typeof o.lIndex?o.lIndex:parseInt(o.lIndex.split("-")[0],10))>e.lIndex&&r--}return r}for(;o.length>0;)for(var l=o.pop(),u=i(r,l.path),p=0,c=l.items;p<c.length;p++){var f,h,d,v,g=c[p];if(g.attrParsed)for(var m in g.attrParsed)a(g,l,g.attrParsed[m].filter(n));else if(g.subparsed)!1!==g.dataBound&&((d=u)[v=g.value]||(d[v]={})),a(g,l,g.subparsed.filter(n));else if(g.cellParsed)for(var y=0,b=g.cellPostParsed;y<b.length;y++){var x=b[y];if("placeholder"===x.type){if("pro-xml-templating/xls-module-loop"===x.module)continue;if(x.subparsed){var w,T;(w=u)[T=x.value]||(w[T]={}),a(x,l,x.subparsed.filter(n))}else{var P,O,S=s(g,l.parents);(P=u=i(r,l.path,S))[O=x.value]||(P[O]={})}}}else!1!==g.dataBound&&((f=u)[h=g.value]||(f[h]={}))}return r},isPlaceholder:n}},945:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){i(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function i(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var s=r(207).pushArray,l=r(830);e.exports=function(e){var t=[],r=e.baseNullGetter,n=e.compiled,o=e.scopeManager;e.nullGetter=function(e,t){return r(e,t||o)},e.resolved=t;var i=[];return Promise.all(n.filter((function(e){return-1===["content","tag"].indexOf(e.type)})).reduce((function(r,n){var u,p=function(e,t){for(var r=0,n=t.modules;r<n.length;r++){var o=n[r].resolve(e,t);if(o)return o}return!1}(n,a(a({},e),{},{resolvedId:l(n,e)}));if(p)u=p.then((function(e){t.push({tag:n.value,lIndex:n.lIndex,value:e})}));else{if("placeholder"!==n.type)return;u=o.getValueAsync(n.value,{part:n}).then((function(t){return null==t?e.nullGetter(n):t})).then((function(e){return t.push({tag:n.value,lIndex:n.lIndex,value:e}),e}))}return r.push(u.catch((function(e){e instanceof Array?s(i,e):i.push(e)}))),r}),[])).then((function(){return{errors:i,resolved:t}}))}},946:function(e,t,r){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function a(e,t,r){return(t=function(e){var t=function(e){if("object"!=n(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,"string");if("object"!=n(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==n(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var i=r(320),s=i.last,l=i.first;function u(e){this.name="GenericError",this.message=e,this.stack=new Error(e).stack}function p(e){this.name="TemplateError",this.message=e,this.stack=new Error(e).stack}function c(e){this.name="RenderingError",this.message=e,this.stack=new Error(e).stack}function f(e){this.name="ScopeParserError",this.message=e,this.stack=new Error(e).stack}function h(e){this.name="InternalError",this.properties={explanation:"InternalError"},this.message=e,this.stack=new Error(e).stack}function d(e){this.name="APIVersionError",this.properties={explanation:"APIVersionError"},this.message=e,this.stack=new Error(e).stack}u.prototype=Error.prototype,p.prototype=new u,c.prototype=new u,f.prototype=new u,h.prototype=new u,d.prototype=new u,e.exports={XTError:u,XTTemplateError:p,XTInternalError:h,XTScopeParserError:f,XTAPIVersionError:d,RenderingError:c,XTRenderingError:c,getClosingTagNotMatchOpeningTag:function(e){var t=e.tags,r=new p("Closing tag does not match opening tag");return r.properties={id:"closing_tag_does_not_match_opening_tag",explanation:'The tag "'.concat(t[0].value,'" is closed by the tag "').concat(t[1].value,'"'),openingtag:l(t).value,offset:[l(t).offset,s(t).offset],closingtag:s(t).value},l(t).square&&(r.properties.square=[l(t).square,s(t).square]),r},getLoopPositionProducesInvalidXMLError:function(e){var t=e.tag,r=e.offset,n=new p('The position of the loop tags "'.concat(t,'" would produce invalid XML'));return n.properties={xtag:t,id:"loop_position_invalid",explanation:'The tags "'.concat(t,'" are misplaced in the document, for example one of them is in a table and the other one outside the table'),offset:r},n},getScopeCompilationError:function(e){var t=e.tag,r=e.rootError,n=e.offset,o=new f("Scope parser compilation failed");return o.properties={id:"scopeparser_compilation_failed",offset:n,xtag:t,explanation:'The scope parser for the tag "'.concat(t,'" failed to compile'),rootError:r},o},getScopeParserExecutionError:function(e){var t=e.tag,r=e.scope,n=e.error,o=e.offset,a=new f("Scope parser execution failed");return a.properties={id:"scopeparser_execution_failed",explanation:"The scope parser for the tag ".concat(t," failed to execute"),scope:r,offset:o,xtag:t,rootError:n},a},getUnclosedTagException:function(e){var t=new p("Unclosed tag");return t.properties={xtag:l(e.xtag.split(" ")).substr(1),id:"unclosed_tag",context:e.xtag,offset:e.offset,lIndex:e.lIndex,explanation:'The tag beginning with "'.concat(e.xtag.substr(0,10),'" is unclosed')},t},getUnopenedTagException:function(e){var t=new p("Unopened tag");return t.properties={xtag:s(e.xtag.split(" ")),id:"unopened_tag",context:e.xtag,offset:e.offset,lIndex:e.lIndex,explanation:'The tag beginning with "'.concat(e.xtag.substr(0,10),'" is unopened')},t},getUnmatchedLoopException:function(e){var t=e.location,r=e.offset,n=e.square,o="start"===t?"unclosed":"unopened",a=new p("".concat("start"===t?"Unclosed":"Unopened"," loop")),i=e.value;return a.properties={id:"".concat(o,"_loop"),explanation:'The loop with tag "'.concat(i,'" is ').concat(o),xtag:i,offset:r},n&&(a.properties.square=n),a},getDuplicateCloseTagException:function(e){var t=new p("Duplicate close tag, expected one close tag");return t.properties={xtag:l(e.xtag.split(" ")),id:"duplicate_close_tag",context:e.xtag,offset:e.offset,lIndex:e.lIndex,explanation:'The tag ending with "'.concat(e.xtag.substr(0,10),'" has duplicate close tags')},t},getDuplicateOpenTagException:function(e){var t=new p("Duplicate open tag, expected one open tag");return t.properties={xtag:l(e.xtag.split(" ")),id:"duplicate_open_tag",context:e.xtag,offset:e.offset,lIndex:e.lIndex,explanation:'The tag beginning with "'.concat(e.xtag.substr(0,10),'" has duplicate open tags')},t},getCorruptCharactersException:function(e){var t=e.tag,r=e.value,n=e.offset,o=new c("There are some XML corrupt characters");return o.properties={id:"invalid_xml_characters",xtag:t,value:r,offset:n,explanation:"There are some corrupt characters for the field ".concat(t)},o},getInvalidRawXMLValueException:function(e){var t=e.tag,r=e.value,n=e.offset,o=new c("Non string values are not allowed for rawXML tags");return o.properties={id:"invalid_raw_xml_value",xtag:t,value:r,offset:n,explanation:"The value of the raw tag : '".concat(t,"' is not a string")},o},getUnbalancedLoopException:function(e,t){var r=new p("Unbalanced loop tag"),n=t[0].part.value,o=t[1].part.value,a=e[0].part.value,i=e[1].part.value;return r.properties={id:"unbalanced_loop_tags",explanation:"Unbalanced loop tags {#".concat(n,"}{/").concat(o,"}{#").concat(a,"}{/").concat(i,"}"),offset:[t[0].part.offset,e[1].part.offset],lastPair:{left:t[0].part.value,right:t[1].part.value},pair:{left:e[0].part.value,right:e[1].part.value}},r},throwApiVersionError:function(e,t){var r=new d(e);throw r.properties=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}({id:"api_version_error"},t),r},throwFileTypeNotHandled:function(e){var t=new h('The filetype "'.concat(e,'" is not handled by docxtemplater'));throw t.properties={id:"filetype_not_handled",explanation:'The file you are trying to generate is of type "'.concat(e,'", but only docx and pptx formats are handled'),fileType:e},t},throwFileTypeNotIdentified:function(e){var t,r=Object.keys(e.files).slice(0,10);t=0===r.length?"Empty zip file":"Zip file contains : ".concat(r.join(","));var n=new h("The filetype for this file could not be identified, is this file corrupted ? ".concat(t));throw n.properties={id:"filetype_not_identified",explanation:"The filetype for this file could not be identified, is this file corrupted ? ".concat(t)},n},throwMalformedXml:function(){var e=new h("Malformed xml");throw e.properties={explanation:"The template contains malformed xml",id:"malformed_xml"},e},throwMultiError:function(e){var t=new p("Multi error");throw t.properties={errors:e,id:"multi_error",explanation:"The template has multiple errors"},t},throwExpandNotFound:function(e){var t=e.part,r=t.value,n=t.offset,o=e.id,a=void 0===o?"raw_tag_outerxml_invalid":o,i=e.message,s=void 0===i?"Raw tag not in paragraph":i,l=e.part,u=e.explanation,c=void 0===u?'The tag "'.concat(r,'" is not inside a paragraph'):u;"function"==typeof c&&(c=c(l));var f=new p(s);throw f.properties={id:a,explanation:c,rootError:e.rootError,xtag:r,offset:n,postparsed:e.postparsed,expandTo:e.expandTo,index:e.index},f},throwRawTagShouldBeOnlyTextInParagraph:function(e){var t=new p("Raw tag should be the only text in paragraph"),r=e.part.value;throw t.properties={id:"raw_xml_tag_should_be_only_text_in_paragraph",explanation:'The raw tag "'.concat(r,'" should be the only text in this paragraph. This means that this tag should not be surrounded by any text or spaces.'),xtag:r,offset:e.part.offset,paragraphParts:e.paragraphParts},t},throwUnimplementedTagType:function(e,t){var r='Unimplemented tag type "'.concat(e.type,'"');e.module&&(r+=' "'.concat(e.module,'"'));var n=new p(r);throw n.properties={part:e,index:t,id:"unimplemented_tag_type"},n},throwXmlTagNotFound:function(e){var t=new p('No tag "'.concat(e.element,'" was found at the ').concat(e.position)),r=e.parsed[e.index];throw t.properties={id:"no_xml_tag_found_at_".concat(e.position),explanation:'No tag "'.concat(e.element,'" was found at the ').concat(e.position),offset:r.offset,part:r,parsed:e.parsed,index:e.index,element:e.element},t},throwXmlInvalid:function(e,t){var r=new p("An XML file has invalid xml");throw r.properties={id:"file_has_invalid_xml",content:e,offset:t,explanation:"The docx contains invalid XML, it is most likely corrupt"},r},throwResolveBeforeCompile:function(){var e=new h("You must run `.compile()` before running `.resolveData()`");throw e.properties={id:"resolve_before_compile",explanation:"You must run `.compile()` before running `.resolveData()`"},e},throwRenderInvalidTemplate:function(){var e=new h("You should not call .render on a document that had compilation errors");throw e.properties={id:"render_on_invalid_template",explanation:"You should not call .render on a document that had compilation errors"},e},throwRenderTwice:function(){var e=new h("You should not call .render twice on the same docxtemplater instance");throw e.properties={id:"render_twice",explanation:"You should not call .render twice on the same docxtemplater instance"},e}}}},t={},r=function r(n){var o=t[n];if(void 0!==o)return o.exports;var a=t[n]={exports:{}};return e[n](a,a.exports,r),a.exports}(807);window.docxtemplater=r}();