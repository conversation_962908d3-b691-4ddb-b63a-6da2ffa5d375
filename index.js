// index.js
const express = require('express');
const cors = require('cors');
require('dotenv').config();

const fs = require('fs');
const path = require('path');
const PizZip = require('pizzip');
const Docxtemplater = require('docxtemplater');
const db = require('./db'); // já configurado em db.js

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

/**
 * Busca no Postgres os campos que correspondem aos placeholders do seu .docx
 */
async function fetchReportData(id) {
  const { rows } = await db.query(
    `
    SELECT
      relatorio                         AS "RELATÓRIO_DA_VISTORIA",
      processo                          AS "PROCESSO",
      concessionaria                    AS "CONCESSIONÁRIA_COMPLETO",
      lote                              AS "Lote",
      edital                            AS "Edital",
      contrato                          AS "Contrato_de_Concessão",
      objeto_fiscalizacao               AS "OBJETO_DA_FISCALIZAÇÃO",
      to_char(data, 'DD "de" TMMonth "de" YYYY') 
                                        AS "data_da_fiscalização_por_extenso"
    FROM vistorias
    WHERE id = $1
    `,
    [id]
  );

  if (!rows.length) {
    throw new Error(`Nenhuma vistoria encontrada com id=${id}`);
  }

  return rows[0];
}

/**
 * Carrega o template, injeta os dados e retorna um buffer do DOCX pronto
 */
async function generateReportBuffer(id) {
  const data = await fetchReportData(id);

  const templatePath = path.resolve(
    __dirname,
    '01-MODELO-RELATÓRIO-FISCALIZAÇÃO EDITADO.docx'
  );
  const content = fs.readFileSync(templatePath, 'binary');
  const zip = new PizZip(content);
  const doc = new Docxtemplater(zip, {
    paragraphLoop: true,
    linebreaks: true,
  });

  doc.setData(data);

  try {
    doc.render();
  } catch (err) {
    console.error('Erro ao renderizar o DOCX:', err);
    throw err;
  }

  return doc.getZip().generate({ type: 'nodebuffer' });
}

// Rota para gerar e baixar o relatório
app.get('/api/vistorias/:id/relatorio', async (req, res) => {
  try {
    const buffer = await generateReportBuffer(req.params.id);
    res
      .status(200)
      .set({
        'Content-Type':
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': `attachment; filename=Relatorio_${req.params.id}.docx`,
      })
      .send(buffer);
  } catch (e) {
    res.status(500).json({ error: e.message });
  }
});



app.listen(PORT, () =>
  console.log(`Servidor rodando na porta ${PORT}`)
);
