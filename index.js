// index.js
const express = require('express');
const cors = require('cors');
require('dotenv').config();

const fs = require('fs');
const path = require('path');
const PizZip = require('pizzip');
const Docxtemplater = require('docxtemplater');

const db = require('./db');
const { generateReport } = require('./generateReport');

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());

app.get('/', (_req, res) => res.send('API do Relatório Automático ok!'));

app.get('/api/vistorias/:id/relatorio', async (req, res) => {
  try {
    // generateReport retorna o path do arquivo ou, se preferir, buffer
    // mas aqui vamos usar o buffer direto:
    const buffer = await (async () => {
      // copiando a função interna de generateReport para retornar buffer
      // (ou você pode alterar generateReport.js para expor generateReportBuffer)
      const data = await (async id => {
        const { rows } = await db.query(
          `SELECT
             descricao                   AS RELATORIO_DA_VISTORIA,
             processo                    AS PROCESSO,
             concessionaria_completo     AS CONCESSIONÁRIA_COMPLETO,
             lote                        AS Lote,
             concessionaria              AS CONCESSIONÁRIA,
             objeto_fiscalizacao         AS OBJETO_DA_FISCALIZAÇÃO,
             area                        AS ÁREA,
             to_char(data_vistoria,
               'DD "de" TMMonth "de" YYYY'
             )                            AS data_da_fiscalização_por_extenso,
             contrato_concessao          AS Contrato_de_Concessão
           FROM fiscalizacoes
           WHERE id = $1`,
          [id]
        );
        if (rows.length === 0) throw new Error(`Nenhuma vistoria com id=${id}`);
        return rows[0];
      })(req.params.id);

      const templatePath = path.resolve(__dirname, '01-MODELO-RELATÓRIO-FISCALIZAÇÃO EDITADO.docx');
      const content = fs.readFileSync(templatePath, 'binary');
      const zip = new PizZip(content);
      const doc = new Docxtemplater(zip, { paragraphLoop: true, linebreaks: true });
      doc.setData(data);
      doc.render();
      return doc.getZip().generate({ type: 'nodebuffer' });
    })();

    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'Content-Disposition': `attachment; filename=relatorio_${req.params.id}.docx`,
    });
    res.send(buffer);

  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err.message });
  }
});

app.listen(PORT, () => console.log(`Servidor rodando na porta ${PORT}`));
