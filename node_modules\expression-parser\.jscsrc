{"disallowSpacesInNamedFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInAnonymousFunctionExpression": {"beforeOpeningRoundBrace": true}, "disallowSpacesInFunctionDeclaration": {"beforeOpeningRoundBrace": true}, "disallowEmptyBlocks": true, "disallowKeywordsOnNewLine": ["else"], "disallowQuotedKeysInObjects": true, "disallowSpacesInsideArrayBrackets": true, "disallowSpacesInsideParentheses": true, "disallowSpaceAfterObjectKeys": true, "disallowSpaceAfterPrefixUnaryOperators": true, "disallowSpaceBeforePostfixUnaryOperators": true, "disallowSpaceBeforeBinaryOperators": [","], "disallowMixedSpacesAndTabs": true, "disallowTrailingWhitespace": true, "disallowYodaConditions": true, "disallowKeywords": ["with"], "disallowMultipleLineStrings": true, "requireSpaceBeforeBlockStatements": true, "requireSpacesInConditionalExpression": true, "requireBlocksOnNewline": 1, "requireCommaBeforeLineBreak": true, "requireSpaceBeforeBinaryOperators": true, "requireSpaceAfterBinaryOperators": true, "requireCamelCaseOrUpperCaseIdentifiers": true, "requireLineFeedAtFileEnd": true, "requireOperatorBeforeLineBreak": true, "requireCapitalizedConstructors": true, "requireCurlyBraces": ["if", "else", "for", "while", "do", "try", "catch"], "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "case", "return", "try", "catch", "typeof"], "maximumLineLength": {"value": 115, "allowComments": true, "allowRegex": true}, "safeContextKeyword": "self", "validateIndentation": 2}