# RelatorioDB.py

import os
from datetime import datetime
from docx import Document
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Dicionário para traduzir meses do inglês para português
MESES_PT = {
    'january': 'janeiro', 'february': 'fevereiro', 'march': 'março',
    'april': 'abril', 'may': 'maio', 'june': 'junho',
    'july': 'julho', 'august': 'agosto', 'september': 'setembro',
    'october': 'outubro', 'november': 'novembro', 'december': 'dezembro'
}

# Constantes para textos condicionais baseados no nível
TEXTO_FATOS_N1 = """Foram identificadas [quantidade] não conformidades de Nível [nível] nas Praças de Pedágio de [localidade 1] e [localidade 2], referentes às câmeras [número da câmera e sentido – ex: câmera 2 (sentido Norte)] e [número da câmera e sentido – ex: câmera 1 (sentido Oeste)], respectivamente, conforme Ordens de Serviço nº [número da OS 1] e [número da OS 2], ambas registradas em [data da OS].
As não conformidades refere-se à [descrição da não conformidade – ex: qualidade da imagem capturada pelo equipamento]."""

TEXTO_CONCLUSAO_N1 = """As não conformidades apontadas neste relatório serão acompanhadas e analisadas pela área técnica da GEFOR e pelas áreas competentes da ARTESP. Dessa forma, poderá ser instaurado processo administrativo sancionatório caso seja constatado o descumprimento das obrigações estabelecidas no Contrato de Concessão."""

TEXTO_FATOS_N2 = """Após vistoria do sistema MIP, não foram identificadas não conformidades de nível 2, referente à qualidade das câmeras de monitoramento."""

TEXTO_CONCLUSAO_N2 = """A qualidade das câmeras de monitoramento será continuamente acompanhada e analisada pela área técnica da GEFOR e pelas instâncias competentes da ARTESP, nos termos da fiscalização contratual aplicável. Em caso de eventual descumprimento das obrigações previstas no Contrato de Concessão, poderá ser instaurado processo administrativo sancionatório."""

def traduzir_data_para_portugues(data_str):
    """Traduz uma data formatada do inglês para português"""
    if not data_str:
        return data_str

    data_lower = data_str.lower()
    for mes_en, mes_pt in MESES_PT.items():
        if mes_en in data_lower:
            return data_str.replace(mes_en.title(), mes_pt)
    return data_str

def traduzir_mes_para_portugues(mes_str):
    """Traduz apenas o mês do inglês para português"""
    if not mes_str:
        return mes_str

    mes_lower = mes_str.lower().strip()
    return MESES_PT.get(mes_lower, mes_str)

def obter_concessionaria_completa(nome_curto):
    """Converte nome curto da concessionária para nome completo"""
    print(f"🔍 BUSCANDO MAPEAMENTO PARA: '{nome_curto}'")

    mapeamento_concessionarias = {
        'Autoban': 'Concessionária do Sistema Anhanguera - Bandeirantes S/A',
        'AutoBan': 'Concessionária do Sistema Anhanguera - Bandeirantes S/A',  # Variação com B maiúsculo
        'Tebe': 'Concessionária de Rodovias Tebe S/A',
        'Intervias': 'Concessionária de Rodovias do Interior Paulista S/A Intervias',
        'Rota das Bandeiras': 'Concessionária Rota das Bandeiras S/A',
        'Triângulo do Sol': 'Concessionária Triângulo do Sol Auto-Estradas S/A',
        'Renovias': 'Renovias Concessionária S/A',
        'Viaoeste': 'Concessionária de Rodovias do Oeste de São Paulo - Viaoeste S/A',
        'Colinas': 'Concessionária Rodovias das Colinas S/A',
        'CART': 'Concessionária Auto Raposo Tavares S/A',
        'ViaRondon': 'Viarondon Concessionária de Rodovia S/A',
        'Spvias': 'Concessionária Rodovias Integradas do Oeste S/A',
        'Rodovias do Tietê': 'Concessionária Rodovias do Tietê S/A',
        'Ecovias': 'Concessionária Ecovias dos Imigrantes S/A',
        'Ecovias Imigrantes': 'Concessionária Ecovias dos Imigrantes S/A',
        'Ecovias Leste Paulista': 'Concessionária Ecovias Leste Paulista S/A',
        'Rodoanel Oeste': 'Concessionária do Rodoanel Oeste S/A',
        'SPMAR': 'Concessionária Litoral Sul S/A',
        'ViaSPSerra': 'Concessionária Rodoanel Norte',
        'Tamoios': 'Concessionária Rodovias dos Tamoios S/A',
        'Entrevias': 'Entrevias Concessionária de Rodovias S/A',
        'ViaPaulista': 'Concessionária Viapaulista S/A',
        'EixoSP': 'Concessionária de Rodovias Piracicaba Panorama S/A - EIXO SP',
        'Ecovias Noroeste Paulista': 'Concessionária Ecovias Noroeste Paulista S.A',
        'Novo Litoral': 'Companhia de Concessões Rodoviárias do Novo Litoral de São Paulo',
        'Rota Sorocabana': 'Concessionária Rota Sorocabana S.A',
        'Ecovias Raposo Castello': 'Concessionária Ecovias Raposo-Castello S.A'
    }

    resultado = mapeamento_concessionarias.get(nome_curto, nome_curto)
    print(f"✅ RESULTADO DO MAPEAMENTO: '{resultado}'")
    return resultado

# 1) Carrega variáveis de ambiente do .env
load_dotenv()

DB_HOST     = os.getenv("DB_HOST")
DB_PORT     = os.getenv("DB_PORT")
DB_NAME     = os.getenv("DB_NAME")       # use DB_NAME aqui
DB_USER     = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")

# Debug: verificar se as variáveis foram carregadas
print(f"DB_HOST: {DB_HOST}")
print(f"DB_PORT: {DB_PORT}")
print(f"DB_NAME: {DB_NAME}")
print(f"DB_USER: {DB_USER}")
print(f"DB_PASSWORD: {DB_PASSWORD}")

# Teste rápido do mapeamento
teste_concessionaria = obter_concessionaria_completa("Ecovias")
print(f"TESTE MAPEAMENTO - 'Ecovias' → '{teste_concessionaria}'")

# 2) Abre conexão com o Postgres
conn = psycopg2.connect(
    host     = DB_HOST,
    port     = DB_PORT,
    dbname   = DB_NAME,
    user     = DB_USER,
    password = DB_PASSWORD,
)

def fetch_vistoria(id):
    """
    Busca um registro na tabela vistorias e retorna um dict com os campos mapeados
    aos seus placeholders.
    """
    with conn.cursor(cursor_factory=RealDictCursor) as cur:
        cur.execute("""
            SET lc_time = 'pt_BR.UTF-8';
            SELECT
                relatorio                         AS descricao,
                processo,
                concessionaria,
                lote,
                edital,
                contrato,
                objeto_fiscalizacao,
                tecnico,
                to_char(data, 'DD "de" TMMonth "de" YYYY') AS data_por_extenso,
                to_char(data, 'TMMonth') AS mes_por_extenso
            FROM vistorias
            WHERE id = %s
        """, (id,))
        row = cur.fetchone()
        if not row:
            raise ValueError(f"Nenhuma vistoria encontrada para id={id}")
        return row

def fetch_vistoria_itens(vistoria_id):
    """Busca itens de uma vistoria pelo ID da vistoria"""
    try:
        # Usa a mesma conexão global
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT
                    descricao,
                    execucao,
                    quantidade,
                    praca,
                    cameras,
                    numero_ordem_servico,
                    descricao_nc
                FROM vistoria_items
                WHERE vistoria_id = %s
                ORDER BY id
            """, (vistoria_id,))

            itens = cur.fetchall()
            return itens
    except Exception as e:
        # Se houver erro, retorna lista vazia
        print(f"Erro ao buscar vistoria_itens: {e}")
        return []

def substituir_placeholders(doc: Document, subs: dict):
    """
    Varre parágrafos e tabelas do documento, substituindo cada chave em subs.
    """
    # Debug específico para concessionária
    concessionaria_tag = '«CONCESSSIONÁRIA_COMPLETO»'
    if concessionaria_tag in subs:
        print(f"DEBUG - Procurando por: '{concessionaria_tag}'")
        print(f"DEBUG - Valor para substituir: '{subs[concessionaria_tag]}'")

    # Parágrafos
    for p in doc.paragraphs:
        for tag, valor in subs.items():
            if tag in p.text:
                if tag == concessionaria_tag:
                    print(f"DEBUG - Encontrou em parágrafo: '{p.text}'")
                p.text = p.text.replace(tag, valor)
                if tag == concessionaria_tag:
                    print(f"DEBUG - Substituído para: '{p.text}'")

    # Tabelas
    for tbl in doc.tables:
        for row in tbl.rows:
            for cell in row.cells:
                for tag, valor in subs.items():
                    if tag in cell.text:
                        if tag == concessionaria_tag:
                            print(f"DEBUG - Encontrou em tabela: '{cell.text}'")
                        cell.text = cell.text.replace(tag, valor)
                        if tag == concessionaria_tag:
                            print(f"DEBUG - Substituído em tabela para: '{cell.text}'")

    # Cabeçalhos e rodapés (caso existam)
    for section in doc.sections:
        for hdr in (section.header, section.footer):
            for p in hdr.paragraphs:
                for tag, valor in subs.items():
                    if tag in p.text:
                        p.text = p.text.replace(tag, valor)
            for tbl in hdr.tables:
                for row in tbl.rows:
                    for cell in row.cells:
                        for tag, valor in subs.items():
                            if tag in cell.text:
                                cell.text = cell.text.replace(tag, valor)

def calcular_total_nc(itens):
    """
    Calcula o total de não conformidades somando o campo 'quantidade' de todos os itens.
    Trata valores inválidos como zero.
    """
    total = 0
    for item in itens:
        quantidade = item.get("quantidade", 0)
        try:
            # Converte para int, tratando None e strings vazias como 0
            if quantidade is None or quantidade == "":
                quantidade = 0
            total += int(quantidade)
        except (ValueError, TypeError):
            # Se não conseguir converter, trata como 0
            continue
    return total

def gerar_relatorio(id, template_path, output_folder):
    """
    Gera um arquivo .docx preenchido com os dados da vistoria `id`.
    """
    # 1) busca dados
    rec = fetch_vistoria(id)

    # 2) busca itens da vistoria
    itens = fetch_vistoria_itens(id)

    # 3) calcula total de não conformidades
    total_nc = calcular_total_nc(itens)
    tem_nc = total_nc > 0

    print(f"🔍 DEBUG - Total de não conformidades: {total_nc}")
    print(f"🔍 DEBUG - Tem não conformidades: {tem_nc}")

    # Captura o valor do nível
    nivel = rec.get("objeto_fiscalizacao", "").upper()  # N1, N2, etc.

    # 4) Define textos condicionais baseados na presença de não conformidades
    if tem_nc:
        # BLOCO A - COM não conformidades
        texto_fatos = ("Foram identificadas [quantidade] não conformidades de Nível [nível] nas Praças de Pedágio de "
                      "[localidade 1] e [localidade 2] referentes às câmeras [número da câmera e sentido – ex: câmera 2 (sentido Norte)] e "
                      "[número da câmera e sentido – ex: câmera 1 (sentido Oeste)], respectivamente, conforme Ordens de Serviço nº "
                      "[número da OS 1] e [número da OS 2], ambas registradas em [data da OS].")

        texto_conclusao = ("A qualidade das câmeras de monitoramento será continuamente acompanhada pela equipe de "
                          "fiscalização desta Agência, com vistas a garantir a efetividade do sistema de monitoramento das praças de pedágio.")
    else:
        # BLOCO B - SEM não conformidades
        texto_fatos = "As não conformidades refere-se à [descrição da não conformidade – ex: qualidade da imagem capturada pelo equipamento]"
        texto_conclusao = "Atenciosamente."

    print(f"🔍 DEBUG - Usando Bloco: {'A (COM NC)' if tem_nc else 'B (SEM NC)'}")
    print(f"🔍 DEBUG - Texto fatos: {texto_fatos[:100]}...")
    print(f"🔍 DEBUG - Texto conclusão: {texto_conclusao}")

    # 5) Processa os textos condicionais substituindo placeholders internos
    # Isso será feito após extrair os dados dos itens

    # 3) prepara dados dos itens
    if itens:
        # Se existem itens no banco, usa os dados reais
        primeiro_item = itens[0]
        segundo_item = itens[1] if len(itens) > 1 else itens[0]

        # Extrai todas as localidades únicas dos itens
        localidades = list(set([item.get("praca", "") for item in itens if item.get("praca")]))

        # Extrai informações das câmeras (filtra valores nulos e vazios)
        cameras_raw = [item.get("cameras", "") for item in itens if item.get("cameras") and item.get("cameras") not in [None, "", "NULL", "[NULL]", "Não sei o que colocar"]]

        # Processa as câmeras (pode ser string JSON ou lista)
        cameras = []
        for cam in cameras_raw:
            if isinstance(cam, list):
                cameras.extend(cam)  # Se já é lista, adiciona todos os elementos
            elif isinstance(cam, str) and cam.startswith('['):
                try:
                    import json
                    cam_list = json.loads(cam)
                    cameras.extend(cam_list)
                except:
                    cameras.append(cam)  # Se não conseguir fazer parse, usa como string
            else:
                cameras.append(str(cam))  # Converte para string

        # Extrai informações das ordens de serviço (filtra valores nulos e vazios)
        ordens_servico = [item.get("numero_ordem_servico", "") for item in itens if item.get("numero_ordem_servico") and item.get("numero_ordem_servico") not in [None, "", "NULL", "[NULL]", "Não sei o que colocar"]]

        # Extrai descrições NC (aceita todos os valores, incluindo "Não sei o que colocar")
        descricoes_nc = [item.get("descricao_nc", "") for item in itens if item.get("descricao_nc") and item.get("descricao_nc") not in [None, "", "NULL", "[NULL]"]]

        # Se não há descrições NC válidas, usa um valor padrão
        if not descricoes_nc:
            descricoes_nc = ["qualidade da imagem capturada pelo equipamento"]

        # Debug: mostra os valores extraídos
        print(f"🔍 DEBUG - Câmeras extraídas: {cameras}")
        print(f"🔍 DEBUG - Ordens de serviço extraídas: {ordens_servico}")
        print(f"🔍 DEBUG - Descrições NC extraídas: {descricoes_nc}")
        print(f"🔍 DEBUG - Localidades extraídas: {localidades}")
        print(f"🔍 DEBUG - Descrição da não conformidade que será usada: '{descricoes_nc[0] if descricoes_nc else 'VAZIO'}'")

    else:
        # Se não existem itens, usa valores padrão
        localidades = ["Santos"]
        cameras = ["câmera 1 (sentido Norte)", "câmera 2 (sentido Sul)"]
        ordens_servico = ["OS001", "OS002"]
        descricoes_nc = ["Descrição NC 1", "Descrição NC 2"]

        primeiro_item = {
            "descricao": "Vistoria técnica remota",
            "execucao": "Remoto",
            "quantidade": "1",
            "praca": "Santos"
        }
        segundo_item = primeiro_item

    # 6) Substitui placeholders internos nos textos condicionais
    # Cria um mapa temporário para substituições internas
    placeholders_internos = {
        "[quantidade]": str(total_nc),
        "[nível]": rec["objeto_fiscalizacao"],
        "[localidade 1]": localidades[0] if len(localidades) > 0 else "",
        "[localidade 2]": localidades[1] if len(localidades) > 1 else localidades[0] if len(localidades) > 0 else "",
        "[número da câmera e sentido – ex: câmera 2 (sentido Norte)]": cameras[0] if len(cameras) > 0 else "",
        "[número da câmera e sentido – ex: câmera 1 (sentido Oeste)]": cameras[1] if len(cameras) > 1 else cameras[0] if len(cameras) > 0 else "",
        "[número da OS 1]": ordens_servico[0] if len(ordens_servico) > 0 else "",
        "[número da OS 2]": ordens_servico[1] if len(ordens_servico) > 1 else ordens_servico[0] if len(ordens_servico) > 0 else "",
        "[descrição da não conformidade – ex: qualidade da imagem capturada pelo equipamento]": descricoes_nc[0] if len(descricoes_nc) > 0 else "",
        "[data da OS]": "",  # Será preenchido após traduzir a data
    }

    # 7) prepara mapa de substituição: placeholder » valor
    # Traduz a data e o mês para português
    data_pt = traduzir_data_para_portugues(rec["data_por_extenso"])
    mes_pt = traduzir_mes_para_portugues(rec["mes_por_extenso"])

    # Atualiza a data da OS nos placeholders internos
    placeholders_internos["[data da OS]"] = data_pt

    # Substitui placeholders internos nos textos condicionais
    for placeholder, valor in placeholders_internos.items():
        texto_fatos = texto_fatos.replace(placeholder, valor)
        texto_conclusao = texto_conclusao.replace(placeholder, valor)

    print(f"🔍 DEBUG - Texto fatos final: {texto_fatos[:150]}...")
    print(f"🔍 DEBUG - Texto conclusão final: {texto_conclusao}")

    # Debug: verificar mapeamento da concessionária
    concessionaria_curta = rec["concessionaria"]
    concessionaria_completa = obter_concessionaria_completa(concessionaria_curta)
    print(f"DEBUG - Concessionária no banco: '{concessionaria_curta}'")
    print(f"DEBUG - Concessionária completa: '{concessionaria_completa}'")

    # Debug: verificar técnico
    tecnico_nome = rec.get("tecnico", "")
    print(f"DEBUG - Técnico no banco: '{tecnico_nome}'")

    subs = {
        "«RELATÓRIO_DA_VISTORIA»":           rec["descricao"],
        "«PROCESSO»":                        str(rec["processo"]),
        "«CONCESSSIONÁRIA_COMPLETO»":        concessionaria_completa,
        "«Lote»":                            str(rec["lote"]),
        "«CONCESSIONÁRIA»":                  rec["concessionaria"],
        "«OBJETO_DA_FISCALIZAÇÃO»":          rec["objeto_fiscalizacao"],
        "«ÁREA»":                            "pedágio",  # Sempre "pedágio"
        "«área_em_minúsculo»":               "pedágio",  # Sempre "pedágio"
        "«data_da_fiscalização_por_extenso»": data_pt,
        "«Mês_por_extenso»":                 mes_pt,
        "«Contrato_de_Concessão»":           rec["contrato"],
        "«Edital»":                          rec["edital"],

        # Novos campos dos itens da vistoria
        "«DESCRIÇÃO»":                       primeiro_item.get("descricao", ""),
        "«remota_ou_em_campo»":              primeiro_item.get("execucao", ""),
        "[quantidade]":                      str(total_nc),  # Usa o total calculado de NC
        "[nível]":                           rec["objeto_fiscalizacao"],  # Mesmo que OBJETO_DA_FISCALIZAÇÃO
        "[localidade 1]":                    localidades[0] if len(localidades) > 0 else "",
        "[localidade 2]":                    localidades[1] if len(localidades) > 1 else localidades[0] if len(localidades) > 0 else "",

        # Campos de câmeras
        "[número da câmera e sentido – ex: câmera 2 (sentido Norte)]": cameras[0] if len(cameras) > 0 else "",
        "[número da câmera e sentido – ex: câmera 1 (sentido Oeste)]": cameras[1] if len(cameras) > 1 else cameras[0] if len(cameras) > 0 else "",

        # Campos de ordens de serviço
        "[número da OS 1]":                  ordens_servico[0] if len(ordens_servico) > 0 else "",
        "[número da OS 2]":                  ordens_servico[1] if len(ordens_servico) > 1 else ordens_servico[0] if len(ordens_servico) > 0 else "",

        # Campos de descrição NC
        "[descrição NC 1]":                  descricoes_nc[0] if len(descricoes_nc) > 0 else "",
        "[descrição NC 2]":                  descricoes_nc[1] if len(descricoes_nc) > 1 else descricoes_nc[0] if len(descricoes_nc) > 0 else "",

        # Campo de descrição da não conformidade (novo placeholder)
        "[descrição da não conformidade – ex: qualidade da imagem capturada pelo equipamento]": descricoes_nc[0] if len(descricoes_nc) > 0 else "",

        # Campo de data da OS (usando a data da vistoria)
        "[data da OS]":                      data_pt,

        # Campos do técnico e área
        "«nome_completo_do_técnico_que_assina»": rec.get("tecnico", ""),
        "«cargo_do_técnico_que_assina»":     "Especialista em Regulação de Transporte II",
        "«áreacondicional»":                 "SUROD-GEFOR-COFOR-PEDÁGIO",

        # Textos condicionais baseados na presença de não conformidades
        "«TEXTO_FATOS»":                     texto_fatos,
        "«TEXTO_CONCLUSAO»":                 texto_conclusao,
    }

    # 3) carrega template
    doc = Document(template_path)

    # 4) faz as substituições
    substituir_placeholders(doc, subs)

    # 5) salva em pasta de saída
    if not os.path.isdir(output_folder):
        os.makedirs(output_folder)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nome_arquivo = f"Relatorio_{id}_{timestamp}.docx"
    caminho = os.path.join(output_folder, nome_arquivo)
    doc.save(caminho)
    print(f"[✓] Relatório gerado em: {caminho}")

def verificar_estrutura_banco():
    """Verifica a estrutura da tabela vistoria_items"""
    print("\n🔍 === VERIFICANDO ESTRUTURA DO BANCO ===")

    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Verifica colunas da tabela
            cur.execute("""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = 'vistoria_items'
                ORDER BY ordinal_position
            """)

            colunas = cur.fetchall()
            print("📋 COLUNAS DA TABELA vistoria_items:")
            for col in colunas:
                print(f"  - {col['column_name']}: {col['data_type']}")

            # Verifica estrutura da tabela vistorias
            cur.execute("""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = 'vistorias'
                ORDER BY ordinal_position
            """)

            colunas_vistorias = cur.fetchall()
            print(f"\n📋 COLUNAS DA TABELA vistorias:")
            for col in colunas_vistorias:
                print(f"  - {col['column_name']}: {col['data_type']}")

            # Verifica dados da vistoria ID 9
            cur.execute("SELECT * FROM vistorias WHERE id = 9")
            vistoria_principal = cur.fetchall()
            print(f"\n📋 VISTORIA PRINCIPAL ID 9: {len(vistoria_principal)} registros")
            if vistoria_principal:
                vistoria = vistoria_principal[0]
                for key, value in vistoria.items():
                    if key == 'tecnico':
                        status = "✅" if value and str(value).strip() else "❌"
                        print(f"    {status} {key}: '{value}'")
                    else:
                        print(f"      {key}: {value}")

            # Verifica dados da vistoria_items para ID 9
            cur.execute("SELECT * FROM vistoria_items WHERE vistoria_id = 9")
            itens = cur.fetchall()

            print(f"\n📦 ITENS DA VISTORIA ID 9 ({len(itens)} registros):")
            for i, item in enumerate(itens, 1):
                print(f"  ITEM {i}:")
                for key, value in item.items():
                    if key in ['cameras', 'numero_ordem_servico', 'descricao_nc']:
                        status = "✅" if value and str(value) not in ['None', 'NULL', '[NULL]', 'Não sei o que colocar'] else "❌"
                        print(f"    {status} {key}: '{value}'")
                    else:
                        print(f"      {key}: {value}")

            print("=" * 50)

    except Exception as e:
        print(f"❌ Erro ao verificar banco: {e}")

if __name__ == "__main__":
    print("=== INICIANDO GERAÇÃO DE RELATÓRIO ===")

    # Verifica estrutura do banco primeiro
    verificar_estrutura_banco()

    # Teste do mapeamento antes de gerar o relatório
    teste = obter_concessionaria_completa("Ecovias")
    print(f"TESTE MAPEAMENTO: 'Ecovias' → '{teste}'")

    # ajuste conforme sua pasta e id de teste
    TEMPLATE  = "01-MODELO-RELATÓRIO-FISCALIZAÇÃO EDITADO.docx"
    SAIDA     = "relatorios_gerados"
    TEST_ID   = 9   # alterado para ID 9

    gerar_relatorio(TEST_ID, TEMPLATE, SAIDA)
