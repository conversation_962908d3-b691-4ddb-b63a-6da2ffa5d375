# RelatorioDB.py

import os
from datetime import datetime
from docx import Document
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Dicionário para traduzir meses do inglês para português
MESES_PT = {
    'january': 'janeiro', 'february': 'fevereiro', 'march': 'março',
    'april': 'abril', 'may': 'maio', 'june': 'junho',
    'july': 'julho', 'august': 'agosto', 'september': 'setembro',
    'october': 'outubro', 'november': 'novembro', 'december': 'dezembro'
}

def traduzir_data_para_portugues(data_str):
    """Traduz uma data formatada do inglês para português"""
    if not data_str:
        return data_str

    data_lower = data_str.lower()
    for mes_en, mes_pt in MESES_PT.items():
        if mes_en in data_lower:
            return data_str.replace(mes_en.title(), mes_pt)
    return data_str

def traduzir_mes_para_portugues(mes_str):
    """Traduz apenas o mês do inglês para português"""
    if not mes_str:
        return mes_str

    mes_lower = mes_str.lower().strip()
    return MESES_PT.get(mes_lower, mes_str)

def obter_concessionaria_completa(nome_curto):
    """Converte nome curto da concessionária para nome completo"""
    mapeamento_concessionarias = {
        'Autoban': 'Concessionária do Sistema Anhanguera - Bandeirantes S/A',
        'Tebe': 'Concessionária de Rodovias Tebe S/A',
        'Intervias': 'Concessionária de Rodovias do Interior Paulista S/A Intervias',
        'Rota das Bandeiras': 'Concessionária Rota das Bandeiras S/A',
        'Triângulo do Sol': 'Concessionária Triângulo do Sol Auto-Estradas S/A',
        'Renovias': 'Renovias Concessionária S/A',
        'Viaoeste': 'Concessionária de Rodovias do Oeste de São Paulo - Viaoeste S/A',
        'Colinas': 'Concessionária Rodovias das Colinas S/A',
        'CART': 'Concessionária Auto Raposo Tavares S/A',
        'ViaRondon': 'Viarondon Concessionária de Rodovia S/A',
        'Spvias': 'Concessionária Rodovias Integradas do Oeste S/A',
        'Rodovias do Tietê': 'Concessionária Rodovias do Tietê S/A',
        'Ecovias': 'Concessionária Ecovias dos Imigrantes S/A',  # Adicionado para "Ecovias"
        'Ecovias Imigrantes': 'Concessionária Ecovias dos Imigrantes S/A',
        'Ecovias Leste Paulista': 'Concessionária Ecovias Leste Paulista S/A',
        'Rodoanel Oeste': 'Concessionária do Rodoanel Oeste S/A',
        'SPMAR': 'Concessionária Litoral Sul S/A',
        'ViaSPSerra': 'Concessionária Rodoanel Norte',
        'Tamoios': 'Concessionária Rodovias dos Tamoios S/A',
        'Entrevias': 'Entrevias Concessionária de Rodovias S/A',
        'ViaPaulista': 'Concessionária Viapaulista S/A',
        'EixoSP': 'Concessionária de Rodovias Piracicaba Panorama S/A - EIXO SP',
        'Ecovias Noroeste Paulista': 'Concessionária Ecovias Noroeste Paulista S.A',
        'Novo Litoral': 'Companhia de Concessões Rodoviárias do Novo Litoral de São Paulo',
        'Rota Sorocabana': 'Concessionária Rota Sorocabana S.A',
        'Ecovias Raposo Castello': 'Concessionária Ecovias Raposo-Castello S.A'
    }

    return mapeamento_concessionarias.get(nome_curto, nome_curto)

# 1) Carrega variáveis de ambiente do .env
load_dotenv()

DB_HOST     = os.getenv("DB_HOST")
DB_PORT     = os.getenv("DB_PORT")
DB_NAME     = os.getenv("DB_NAME")       # use DB_NAME aqui
DB_USER     = os.getenv("DB_USER")
DB_PASSWORD = os.getenv("DB_PASSWORD")

# Debug: verificar se as variáveis foram carregadas
print(f"DB_HOST: {DB_HOST}")
print(f"DB_PORT: {DB_PORT}")
print(f"DB_NAME: {DB_NAME}")
print(f"DB_USER: {DB_USER}")
print(f"DB_PASSWORD: {DB_PASSWORD}")

# Teste rápido do mapeamento
teste_concessionaria = obter_concessionaria_completa("Ecovias")
print(f"TESTE MAPEAMENTO - 'Ecovias' → '{teste_concessionaria}'")

# 2) Abre conexão com o Postgres
conn = psycopg2.connect(
    host     = DB_HOST,
    port     = DB_PORT,
    dbname   = DB_NAME,
    user     = DB_USER,
    password = DB_PASSWORD,
)

def fetch_vistoria(id):
    """
    Busca um registro na tabela vistorias e retorna um dict com os campos mapeados
    aos seus placeholders.
    """
    with conn.cursor(cursor_factory=RealDictCursor) as cur:
        cur.execute("""
            SET lc_time = 'pt_BR.UTF-8';
            SELECT
                relatorio                         AS descricao,
                processo,
                concessionaria,
                lote,
                edital,
                contrato,
                objeto_fiscalizacao,
                to_char(data, 'DD "de" TMMonth "de" YYYY') AS data_por_extenso,
                to_char(data, 'TMMonth') AS mes_por_extenso
            FROM vistorias
            WHERE id = %s
        """, (id,))
        row = cur.fetchone()
        if not row:
            raise ValueError(f"Nenhuma vistoria encontrada para id={id}")
        return row

def fetch_vistoria_itens(vistoria_id):
    """Busca itens de uma vistoria pelo ID da vistoria"""
    try:
        # Usa a mesma conexão global
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT
                    descricao,
                    execucao,
                    quantidade,
                    praca
                FROM vistoria_items
                WHERE vistoria_id = %s
                ORDER BY id
            """, (vistoria_id,))

            itens = cur.fetchall()
            return itens
    except Exception as e:
        # Se houver erro, retorna lista vazia
        print(f"Erro ao buscar vistoria_itens: {e}")
        return []

def substituir_placeholders(doc: Document, subs: dict):
    """
    Varre parágrafos e tabelas do documento, substituindo cada chave em subs.
    """
    # Parágrafos
    for p in doc.paragraphs:
        for tag, valor in subs.items():
            if tag in p.text:
                p.text = p.text.replace(tag, valor)

    # Tabelas
    for tbl in doc.tables:
        for row in tbl.rows:
            for cell in row.cells:
                for tag, valor in subs.items():
                    if tag in cell.text:
                        cell.text = cell.text.replace(tag, valor)

    # Cabeçalhos e rodapés (caso existam)
    for section in doc.sections:
        for hdr in (section.header, section.footer):
            for p in hdr.paragraphs:
                for tag, valor in subs.items():
                    if tag in p.text:
                        p.text = p.text.replace(tag, valor)
            for tbl in hdr.tables:
                for row in tbl.rows:
                    for cell in row.cells:
                        for tag, valor in subs.items():
                            if tag in cell.text:
                                cell.text = cell.text.replace(tag, valor)

def gerar_relatorio(id, template_path, output_folder):
    """
    Gera um arquivo .docx preenchido com os dados da vistoria `id`.
    """
    # 1) busca dados
    rec = fetch_vistoria(id)

    # 2) busca itens da vistoria
    itens = fetch_vistoria_itens(id)

    # 3) prepara dados dos itens
    if itens:
        # Se existem itens no banco, usa os dados reais
        primeiro_item = itens[0]
        # Extrai todas as localidades únicas dos itens
        localidades = list(set([item.get("praca", "") for item in itens if item.get("praca")]))
    else:
        # Se não existem itens, usa valores padrão
        # Como não existe coluna praca na tabela vistorias, usa valor padrão
        localidades = ["Santos"]  # Valor padrão
        primeiro_item = {
            "descricao": "Vistoria técnica remota",
            "execucao": "Remoto",
            "quantidade": "1",
            "praca": "Santos"
        }

    # 4) prepara mapa de substituição: placeholder » valor
    # Traduz a data e o mês para português
    data_pt = traduzir_data_para_portugues(rec["data_por_extenso"])
    mes_pt = traduzir_mes_para_portugues(rec["mes_por_extenso"])

    # Debug: verificar mapeamento da concessionária
    concessionaria_curta = rec["concessionaria"]
    concessionaria_completa = obter_concessionaria_completa(concessionaria_curta)
    print(f"DEBUG - Concessionária no banco: '{concessionaria_curta}'")
    print(f"DEBUG - Concessionária completa: '{concessionaria_completa}'")

    subs = {
        "«RELATÓRIO_DA_VISTORIA»":           rec["descricao"],
        "«PROCESSO»":                        str(rec["processo"]),
        "«CONCESSSIONÁRIA_COMPLETO»":        concessionaria_completa,
        "«Lote»":                            str(rec["lote"]),
        "«CONCESSIONÁRIA»":                  rec["concessionaria"],
        "«OBJETO_DA_FISCALIZAÇÃO»":          rec["objeto_fiscalizacao"],
        "«ÁREA»":                            "pedágio",  # Sempre "pedágio"
        "«área_em_minúsculo»":               "pedágio",  # Sempre "pedágio"
        "«data_da_fiscalização_por_extenso»": data_pt,
        "«Mês_por_extenso»":                 mes_pt,
        "«Contrato_de_Concessão»":           rec["contrato"],
        "«Edital»":                          rec["edital"],

        # Novos campos dos itens da vistoria
        "«DESCRIÇÃO»":                       primeiro_item.get("descricao", ""),
        "«remota_ou_em_campo»":              primeiro_item.get("execucao", ""),
        "[quantidade]":                      str(primeiro_item.get("quantidade", "")),
        "[nível]":                           rec["objeto_fiscalizacao"],  # Mesmo que OBJETO_DA_FISCALIZAÇÃO
        "[localidade 1]":                    localidades[0] if len(localidades) > 0 else "",
        "[localidade 2]":                    localidades[1] if len(localidades) > 1 else "",
    }

    # 3) carrega template
    doc = Document(template_path)

    # 4) faz as substituições
    substituir_placeholders(doc, subs)

    # 5) salva em pasta de saída
    if not os.path.isdir(output_folder):
        os.makedirs(output_folder)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    nome_arquivo = f"Relatorio_{id}_{timestamp}.docx"
    caminho = os.path.join(output_folder, nome_arquivo)
    doc.save(caminho)
    print(f"[✓] Relatório gerado em: {caminho}")

if __name__ == "__main__":
    # ajuste conforme sua pasta e id de teste
    TEMPLATE  = "01-MODELO-RELATÓRIO-FISCALIZAÇÃO EDITADO.docx"
    SAIDA     = "relatorios_gerados"
    TEST_ID   = 7   # altere para outro id se quiser

    gerar_relatorio(TEST_ID, TEMPLATE, SAIDA)
