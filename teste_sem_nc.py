#!/usr/bin/env python3
"""
Script para testar cenário SEM não-conformidades
"""
import psycopg2
from psycopg2.extras import RealDictCursor
import os

# Configurações do banco
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = int(os.getenv("DB_PORT", "5432"))
DB_NAME = os.getenv("DB_NAME", "relatorio_db")
DB_USER = os.getenv("DB_USER", "admin")
DB_PASSWORD = os.getenv("DB_PASSWORD", "password123")

def testar_sem_nc():
    """Altera quantidade para 0 e testa geração de relatório"""
    try:
        # Conecta ao banco
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
        )
        
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Altera quantidade para 0
            cur.execute("UPDATE vistoria_items SET quantidade = 0 WHERE vistoria_id = 9")
            conn.commit()
            print("✅ Quantidade alterada para 0")
            
            # Verifica alteração
            cur.execute("SELECT quantidade FROM vistoria_items WHERE vistoria_id = 9")
            result = cur.fetchone()
            print(f"🔍 Nova quantidade: {result['quantidade']}")
        
        conn.close()
        
        # Agora executa o relatório
        print("\n=== EXECUTANDO RELATÓRIO COM QUANTIDADE = 0 ===")
        import subprocess
        result = subprocess.run(["python", "RelatorioDB.py"], capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("ERRO:", result.stderr)
            
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    testar_sem_nc()
