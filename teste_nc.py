#!/usr/bin/env python3
import psycopg2
from psycopg2.extras import RealDictCursor

# Configurações do banco
DB_HOST = "localhost"
DB_PORT = 5432
DB_NAME = "relatorio_db"
DB_USER = "admin"
DB_PASSWORD = "password123"

def atualizar_quantidade_nc(vistoria_id, nova_quantidade):
    """Atualiza a quantidade de não conformidades para testar a lógica"""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
        )
        
        with conn.cursor() as cur:
            cur.execute("""
                UPDATE vistoria_items 
                SET quantidade = %s 
                WHERE vistoria_id = %s
            """, (nova_quantidade, vistoria_id))
            
            conn.commit()
            print(f"✅ Quantidade atualizada para {nova_quantidade} na vistoria {vistoria_id}")
            
            # Verifica a atualização
            cur.execute("SELECT quantidade FROM vistoria_items WHERE vistoria_id = %s", (vistoria_id,))
            resultado = cur.fetchone()
            print(f"🔍 Quantidade atual no banco: {resultado[0]}")
            
    except Exception as e:
        print(f"❌ Erro ao atualizar: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    # Atualiza para 2 não conformidades para testar o Bloco A
    atualizar_quantidade_nc(9, 2)
