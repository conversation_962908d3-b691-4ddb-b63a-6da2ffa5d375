import os
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def verificar_dados_banco():
    """Verifica os dados atuais no banco de dados"""
    
    try:
        # Conecta ao banco
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT'),
            database=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD')
        )
        
        print("🔍 === VERIFICANDO DADOS NO BANCO ===\n")
        
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # 1. Verifica a estrutura da tabela vistoria_items
            print("📋 ESTRUTURA DA TABELA vistoria_items:")
            cur.execute("""
                SELECT column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name = 'vistoria_items'
                ORDER BY ordinal_position
            """)
            
            colunas = cur.fetchall()
            for col in colunas:
                print(f"  - {col['column_name']}: {col['data_type']} ({'NULL' if col['is_nullable'] == 'YES' else 'NOT NULL'})")
            
            print("\n" + "="*50)
            
            # 2. Verifica dados da vistoria ID 7
            print("\n📊 DADOS DA VISTORIA ID 7:")
            cur.execute("""
                SELECT * FROM vistorias WHERE id = 7
            """)
            
            vistoria = cur.fetchone()
            if vistoria:
                print("✅ Vistoria encontrada:")
                for key, value in vistoria.items():
                    print(f"  {key}: {value}")
            else:
                print("❌ Vistoria ID 7 não encontrada!")
                return
            
            print("\n" + "="*50)
            
            # 3. Verifica itens da vistoria ID 7
            print("\n📦 ITENS DA VISTORIA ID 7:")
            cur.execute("""
                SELECT * FROM vistoria_items WHERE vistoria_id = 7 ORDER BY id
            """)
            
            itens = cur.fetchall()
            if itens:
                print(f"✅ Encontrados {len(itens)} itens:")
                for i, item in enumerate(itens, 1):
                    print(f"\n  📌 ITEM {i} (ID: {item['id']}):")
                    for key, value in item.items():
                        # Destaca campos importantes
                        if key in ['cameras', 'numero_ordem_servico', 'descricao_nc']:
                            status = "✅" if value and value not in ['NULL', '[NULL]', 'Não sei o que colocar'] else "❌"
                            print(f"    {status} {key}: '{value}'")
                        else:
                            print(f"      {key}: {value}")
            else:
                print("❌ Nenhum item encontrado para vistoria_id = 7!")
            
            print("\n" + "="*50)
            
            # 4. Verifica se as colunas existem
            print("\n🔍 VERIFICAÇÃO DE COLUNAS NECESSÁRIAS:")
            colunas_necessarias = ['cameras', 'numero_ordem_servico', 'descricao_nc']
            
            for coluna in colunas_necessarias:
                cur.execute("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'vistoria_items' AND column_name = %s
                """, (coluna,))
                
                existe = cur.fetchone()
                if existe:
                    print(f"  ✅ Coluna '{coluna}' existe")
                    
                    # Verifica se tem dados
                    cur.execute(f"""
                        SELECT COUNT(*) as total, 
                               COUNT({coluna}) as preenchidos
                        FROM vistoria_items 
                        WHERE vistoria_id = 7
                    """)
                    stats = cur.fetchone()
                    print(f"      Total registros: {stats['total']}, Preenchidos: {stats['preenchidos']}")
                else:
                    print(f"  ❌ Coluna '{coluna}' NÃO existe - precisa ser criada!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erro ao conectar ao banco: {e}")

if __name__ == "__main__":
    verificar_dados_banco()
