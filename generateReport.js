// index.js
const express = require('express');
const cors = require('cors');
require('dotenv').config({ path: require('path').resolve(__dirname, './.env') });

const fs = require('fs');
const path = require('path');
const PizZip = require('pizzip');
const Docxtemplater = require('docxtemplater');

const app = express();
const PORT = process.env.PORT || 3001;
const db = require('./db'); // sua conexão Postgres já configurada :contentReference[oaicite:0]{index=0}

// Middlewares
app.use(cors({ origin: '*', methods: ['GET','POST','PUT','DELETE','OPTIONS'], allowedHeaders: ['Content-Type','Authorization'] }));
app.use(express.json());

// Função para buscar dados da vistoria e formatar para o template
async function fetchReportData(id) {
  const { rows } = await db.query(
    `SELECT
       descricao                   AS RELATORIO_DA_VISTORIA,
       processo                    AS PROCESSO,
       concessionaria_completo     AS CONCESSIONÁRIA_COMPLETO,
       lote                        AS Lote,
       concessionaria              AS CONCESSIONÁRIA,
       objeto_fiscalizacao         AS OBJETO_DA_FISCALIZAÇÃO,
       area                        AS ÁREA,
       to_char(data_vistoria,
         'DD "de" TMMonth "de" YYYY'
       )                            AS data_da_fiscalização_por_extenso,
       contrato_concessao          AS Contrato_de_Concessão
     FROM fiscalizacoes
     WHERE id = $1;`,
    [id]
  );
  if (rows.length === 0) {
    throw new Error(`Nenhuma vistoria encontrada para id=${id}`);
  }
  return rows[0];
}

// Função que carrega o template, injeta os dados e retorna o buffer do DOCX
async function generateReportBuffer(id) {
  const data = await fetchReportData(id);
  const templatePath = path.resolve(__dirname, '01-MODELO-RELATÓRIO-FISCALIZAÇÃO EDITADO.docx');
  const content = fs.readFileSync(templatePath, 'binary');
  const zip = new PizZip(content);
  const doc = new Docxtemplater(zip, { paragraphLoop: true, linebreaks: true });

  doc.setData(data);
  try {
    doc.render();
  } catch (err) {
    console.error('Erro ao renderizar o DOCX:', err);
    throw err;
  }
  return doc.getZip().generate({ type: 'nodebuffer' });
}

// Rota para gerar e enviar o relatório preenchido
app.get('/api/vistorias/:id/relatorio', async (req, res) => {
  const id = req.params.id;
  try {
    const buffer = await generateReportBuffer(id);
    res.set({
      'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'Content-Disposition': `attachment; filename=Relatorio_${id}.docx`,
    });
    res.send(buffer);
  } catch (e) {
    console.error('Erro ao gerar relatório:', e);
    res.status(500).json({ error: e.message });
  }
});

// Rota de teste simples
app.get('/', (_req, res) => res.send('API do Relatório Automático funcionando!'));

// Iniciar o servidor
app.listen(PORT, () => {
  console.log(`Servidor rodando na porta ${PORT}`);
});
