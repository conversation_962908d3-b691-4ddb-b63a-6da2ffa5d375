import psycopg2
from psycopg2.extras import RealDictCursor
import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def verificar_tabelas_existentes():
    """Verifica todas as tabelas existentes no banco"""
    try:
        # Conecta ao banco
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT'),
            database=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD')
        )
        
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Lista todas as tabelas
            print("=== TODAS AS TABELAS NO BANCO ===")
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name
            """)
            tabelas = cur.fetchall()
            
            for tabela in tabelas:
                print(f"Tabela: {tabela['table_name']}")
            
            print("\n=== TENTANDO ACESSAR vistoria_itens ===")
            try:
                cur.execute("SELECT COUNT(*) as total FROM vistoria_itens")
                total = cur.fetchone()['total']
                print(f"✅ Tabela vistoria_itens existe! Total de registros: {total}")
                
                if total > 0:
                    cur.execute("SELECT * FROM vistoria_itens LIMIT 3")
                    registros = cur.fetchall()
                    print("Primeiros registros:")
                    for reg in registros:
                        print(f"  ID: {reg.get('id')}, Vistoria ID: {reg.get('vistoria_id')}, Praça: {reg.get('praca')}")
                        
            except Exception as e:
                print(f"❌ Erro ao acessar vistoria_itens: {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"Erro geral: {e}")

if __name__ == "__main__":
    verificar_tabelas_existentes()
