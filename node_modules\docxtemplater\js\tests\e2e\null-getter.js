"use strict";

var _require = require("../utils.js"),
  resolveSoon = _require.resolveSoon;
describe("Nullgetter", function () {
  it("should call nullgetter for loops synchonously", function () {
    return this.render({
      name: "multi-loop.docx",
      data: {
        test2: "Value2"
      },
      options: {
        paragraphLoop: true,
        nullGetter: function nullGetter(part) {
          if (part.module === "loop") {
            return [{
              name: "Acme",
              users: [{
                name: "<PERSON>"
              }, {
                name: "<PERSON>"
              }]
            }, {
              name: "<PERSON><PERSON>",
              users: [{
                name: "<PERSON>"
              }, {
                name: "<PERSON>"
              }]
            }];
          }
        }
      },
      expectedName: "expected-multi-loop.docx"
    });
  });
  it("should call nullgetter for loops async", function () {
    return this.render({
      name: "multi-loop.docx",
      data: {
        test2: "Value2"
      },
      options: {
        paragraphLoop: true,
        nullGetter: function nullGetter(part) {
          if (part.module === "loop") {
            return resolveSoon([{
              name: "<PERSON>c<PERSON>",
              users: resolveSoon([{
                name: resolve<PERSON>oon("<PERSON>", 25)
              }, resolve<PERSON>oon({
                name: "<PERSON>"
              })], 5)
            }, resolveSoon({
              name: resolve<PERSON><PERSON>("<PERSON><PERSON>"),
              users: resolve<PERSON>oon([{
                name: "<PERSON>"
              }, {
                name: "Liz"
              }])
            }, 20)]);
          }
        },
        async: true
      },
      expectedName: "expected-multi-loop.docx",
      async: true
    });
  });
  it("should call nullGetter with empty rawxml", function () {
    return this.renderV4({
      name: "table-raw-xml.docx",
      options: {
        nullGetter: function nullGetter(part) {
          if (part.module === "rawxml") {
            return "<w:p>\n                        <w:r>\n                            <w:rPr><w:color w:val=\"FF0000\"/></w:rPr>\n                            <w:t>UNDEFINED</w:t>\n                        </w:r>\n                        </w:p>";
          }
        }
      },
      expectedName: "expected-raw-xml-null.docx"
    });
  });
});