import os
import psycopg2
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def inserir_dados_teste():
    """Insere dados de teste nos campos novos da tabela vistoria_items"""
    
    try:
        # Conecta ao banco
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT'),
            database=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD')
        )
        
        with conn.cursor() as cur:
            # Primeiro, vamos ver o que existe na tabela
            print("=== DADOS ATUAIS ===")
            cur.execute("""
                SELECT id, vistoria_id, descricao, cameras, numero_ordem_servico, descricao_nc 
                FROM vistoria_items 
                WHERE vistoria_id = 7
            """)
            
            registros = cur.fetchall()
            for reg in registros:
                print(f"ID: {reg[0]}, Vistoria: {reg[1]}, Descrição: {reg[2]}")
                print(f"  Cameras: {reg[3]}")
                print(f"  OS: {reg[4]}")
                print(f"  NC: {reg[5]}")
                print("---")
            
            if not registros:
                print("❌ Nenhum registro encontrado para vistoria_id = 7")
                return
            
            # Atualiza os registros com dados de teste
            print("\n=== INSERINDO DADOS DE TESTE ===")
            
            # Se há apenas 1 registro, atualiza ele
            if len(registros) == 1:
                cur.execute("""
                    UPDATE vistoria_items SET 
                        cameras = 'câmera 1 (sentido Norte)',
                        numero_ordem_servico = 'OS-2024-001',
                        descricao_nc = 'Não conformidade detectada na praça de pedágio'
                    WHERE id = %s
                """, (registros[0][0],))
                print(f"✅ Atualizado registro ID {registros[0][0]}")
            
            # Se há 2+ registros, atualiza com valores diferentes
            elif len(registros) >= 2:
                # Primeiro registro
                cur.execute("""
                    UPDATE vistoria_items SET 
                        cameras = 'câmera 1 (sentido Norte)',
                        numero_ordem_servico = 'OS-2024-001',
                        descricao_nc = 'Primeira não conformidade detectada'
                    WHERE id = %s
                """, (registros[0][0],))
                print(f"✅ Atualizado registro ID {registros[0][0]} (primeiro)")
                
                # Segundo registro
                cur.execute("""
                    UPDATE vistoria_items SET 
                        cameras = 'câmera 2 (sentido Sul)',
                        numero_ordem_servico = 'OS-2024-002',
                        descricao_nc = 'Segunda não conformidade detectada'
                    WHERE id = %s
                """, (registros[1][0],))
                print(f"✅ Atualizado registro ID {registros[1][0]} (segundo)")
            
            # Confirma as mudanças
            conn.commit()
            print("\n✅ Dados de teste inseridos com sucesso!")
            
            # Mostra os dados atualizados
            print("\n=== DADOS APÓS ATUALIZAÇÃO ===")
            cur.execute("""
                SELECT id, vistoria_id, descricao, cameras, numero_ordem_servico, descricao_nc 
                FROM vistoria_items 
                WHERE vistoria_id = 7
            """)
            
            registros_atualizados = cur.fetchall()
            for reg in registros_atualizados:
                print(f"ID: {reg[0]}, Vistoria: {reg[1]}")
                print(f"  Cameras: '{reg[3]}'")
                print(f"  OS: '{reg[4]}'")
                print(f"  NC: '{reg[5]}'")
                print("---")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erro: {e}")

if __name__ == "__main__":
    inserir_dados_teste()
