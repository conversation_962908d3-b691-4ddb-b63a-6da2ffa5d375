import psycopg2
from psycopg2.extras import RealDictCursor
import os
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

def verificar_tabelas():
    """Verifica os dados nas tabelas do banco"""
    try:
        # Conecta ao banco
        conn = psycopg2.connect(
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT'),
            database=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD')
        )
        
        with conn.cursor(cursor_factory=RealDictCursor) as cur:
            # Verifica tabela vistorias
            print("=== TABELA VISTORIAS ===")
            cur.execute("SELECT COUNT(*) as total FROM vistorias")
            total = cur.fetchone()['total']
            print(f"Total de registros: {total}")
            
            if total > 0:
                cur.execute("SELECT * FROM vistorias LIMIT 5")
                registros = cur.fetchall()
                for reg in registros:
                    print(f"ID: {reg['id']}, Relatório: {reg.get('relatorio', 'N/A')}")
            
            print("\n=== TABELA VISTORIA_ITENS ===")
            cur.execute("SELECT COUNT(*) as total FROM vistoria_itens")
            total_itens = cur.fetchone()['total']
            print(f"Total de registros: {total_itens}")
            
            if total_itens > 0:
                cur.execute("SELECT * FROM vistoria_itens LIMIT 5")
                itens = cur.fetchall()
                for item in itens:
                    print(f"ID: {item['id']}, Vistoria ID: {item.get('vistoria_id', 'N/A')}, Descrição: {item.get('descricao', 'N/A')}")
        
        conn.close()
        
    except Exception as e:
        print(f"Erro ao verificar dados: {e}")

if __name__ == "__main__":
    verificar_tabelas()
