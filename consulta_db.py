#!/usr/bin/env python3
import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv

# Carrega variáveis de ambiente
load_dotenv()

print("🚀 INICIANDO CONSULTA AO BANCO DE DADOS")
print("=" * 60)

try:
    # Conecta ao banco
    conn = psycopg2.connect(
        host=os.getenv('DB_HOST'),
        port=os.getenv('DB_PORT'),
        database=os.getenv('DB_NAME'),
        user=os.getenv('DB_USER'),
        password=os.getenv('DB_PASSWORD')
    )
    
    print("✅ Conexão estabelecida com sucesso!")
    
    with conn.cursor(cursor_factory=RealDictCursor) as cur:
        # Verifica estrutura da tabela
        print("\n📋 ESTRUTURA DA TABELA vistoria_items:")
        cur.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'vistoria_items'
            ORDER BY ordinal_position
        """)
        
        colunas = cur.fetchall()
        for col in colunas:
            print(f"  - {col['column_name']}: {col['data_type']}")
        
        # Verifica dados da vistoria
        print(f"\n📊 DADOS DA VISTORIA ID 7:")
        cur.execute("SELECT * FROM vistorias WHERE id = 7")
        vistoria = cur.fetchone()
        
        if vistoria:
            for key, value in vistoria.items():
                print(f"  {key}: {value}")
        else:
            print("❌ Vistoria não encontrada!")
            sys.exit(1)
        
        # Verifica itens da vistoria
        print(f"\n📦 ITENS DA VISTORIA ID 7:")
        cur.execute("SELECT * FROM vistoria_items WHERE vistoria_id = 7")
        itens = cur.fetchall()
        
        if itens:
            print(f"✅ Encontrados {len(itens)} itens:")
            for i, item in enumerate(itens, 1):
                print(f"\n  ITEM {i}:")
                for key, value in item.items():
                    print(f"    {key}: {value}")
        else:
            print("❌ Nenhum item encontrado!")
    
    conn.close()
    print("\n✅ Consulta concluída!")
    
except Exception as e:
    print(f"❌ ERRO: {e}")
    sys.exit(1)
