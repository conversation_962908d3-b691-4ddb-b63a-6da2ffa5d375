"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var _require = require("../doc-utils.js"),
  pushArray = _require.pushArray;
var wrapper = require("../module-wrapper.js");
var filetypes = require("../filetypes.js");
var _require2 = require("../content-types.js"),
  settingsContentType = _require2.settingsContentType,
  coreContentType = _require2.coreContentType,
  appContentType = _require2.appContentType,
  customContentType = _require2.customContentType,
  diagramDataContentType = _require2.diagramDataContentType,
  diagramDrawingContentType = _require2.diagramDrawingContentType;
var commonContentTypes = [settingsContentType, coreContentType, appContentType, customContentType, diagramDataContentType, diagramDrawingContentType];
var Common = /*#__PURE__*/function () {
  function Common() {
    _classCallCheck(this, Common);
    this.name = "Common";
  }
  return _createClass(Common, [{
    key: "getFileType",
    value: function getFileType(_ref) {
      var doc = _ref.doc;
      var invertedContentTypes = doc.invertedContentTypes;
      if (!invertedContentTypes) {
        return;
      }
      for (var _i2 = 0; _i2 < commonContentTypes.length; _i2++) {
        var ct = commonContentTypes[_i2];
        if (invertedContentTypes[ct]) {
          pushArray(doc.targets, invertedContentTypes[ct]);
        }
      }
      var keys = ["docx", "pptx", "xlsx"];
      var ftCandidate;
      for (var _i4 = 0; _i4 < keys.length; _i4++) {
        var key = keys[_i4];
        var contentTypes = filetypes[key];
        for (var _i6 = 0; _i6 < contentTypes.length; _i6++) {
          var _ct = contentTypes[_i6];
          if (invertedContentTypes[_ct]) {
            for (var _i8 = 0, _invertedContentTypes2 = invertedContentTypes[_ct]; _i8 < _invertedContentTypes2.length; _i8++) {
              var target = _invertedContentTypes2[_i8];
              if (doc.relsTypes[target] && ["http://purl.oclc.org/ooxml/officeDocument/relationships/officeDocument", "http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument"].indexOf(doc.relsTypes[target]) === -1) {
                continue;
              }
              ftCandidate = key;
              if (filetypes.main.indexOf(_ct) !== -1 || _ct === filetypes.pptx[0]) {
                doc.textTarget || (doc.textTarget = target);
              }
              if (ftCandidate === "xlsx") {
                continue;
              }
              doc.targets.push(target);
            }
          }
        }
        if (ftCandidate) {
          return ftCandidate;
        }
      }
      return ftCandidate;
    }
  }]);
}();
module.exports = function () {
  return wrapper(new Common());
};