"use strict";

var _require = require("../utils.js"),
  createDocV4 = _require.createDocV4,
  shouldBeSame = _require.shouldBeSame,
  expect = _require.expect;
describe("Docx document properties", function () {
  it("should change values in doc-props", function () {
    var doc = createDocV4("tag-docprops.docx", {
      paragraphLoop: true
    });
    expect(doc.getFullText("docProps/app.xml")).to.be.equal("TitleName: {first_name}");
    doc.render({
      first_name: "<PERSON><PERSON>",
      last_name: "<PERSON>",
      phone: "0652455478",
      description: "New Website"
    });
    expect(doc.getFullText()).to.be.equal("Edgar Hipp");
    expect(doc.getFullText("word/header1.xml")).to.be.equal("Edgar Hipp0652455478New Website");
    expect(doc.getFullText("word/footer1.xml")).to.be.equal("EdgarHipp0652455478");
    expect(doc.getFullText("docProps/app.xml")).to.be.equal("TitleName: Hipp");
    shouldBeSame({
      doc: doc,
      expectedName: "expected-tag-docprops.docx"
    });
  });
  it("should change custom values inside '<vt:lpwstr>' in file docProps/custom.xml", function () {
    return this.render({
      name: "tag-docprops-in-doc.docx",
      data: {
        first_name: "Hipp",
        email: "<EMAIL>",
        last_name: "Edgar",
        phone: "0652455478",
        description: "New Website"
      },
      expectedName: "expected-tag-docprops-in-doc.docx"
    });
  });
  it("should be possible to ignore files in docProps/core.xml", function () {
    var avoidRenderingCoreXMLModule = {
      name: "avoidRenderingCoreXMLModule",
      getFileType: function getFileType(_ref) {
        var doc = _ref.doc;
        doc.targets = doc.targets.filter(function (file) {
          if (file === "docProps/core.xml" || file === "docProps/app.xml") {
            return false;
          }
          return true;
        });
      }
    };
    var doc = createDocV4("core-xml-missing-close-tag.docx", {
      modules: [avoidRenderingCoreXMLModule]
    });
    doc.render({
      first_name: "Hipp",
      last_name: "Edgar",
      phone: "0652455478",
      description: "New Website"
    });
    shouldBeSame({
      doc: doc,
      expectedName: "expected-core-xml.docx"
    });
  });
});