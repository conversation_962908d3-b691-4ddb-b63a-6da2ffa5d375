[{"/home/<USER>/www/pizzip/es6/arrayReader.js": "1", "/home/<USER>/www/pizzip/es6/base64.js": "2", "/home/<USER>/www/pizzip/es6/compressedObject.js": "3", "/home/<USER>/www/pizzip/es6/compressions.js": "4", "/home/<USER>/www/pizzip/es6/crc32.js": "5", "/home/<USER>/www/pizzip/es6/dataReader.js": "6", "/home/<USER>/www/pizzip/es6/defaults.js": "7", "/home/<USER>/www/pizzip/es6/deprecatedPublicUtils.js": "8", "/home/<USER>/www/pizzip/es6/flate.js": "9", "/home/<USER>/www/pizzip/es6/index.js": "10", "/home/<USER>/www/pizzip/es6/license_header.js": "11", "/home/<USER>/www/pizzip/es6/load.js": "12", "/home/<USER>/www/pizzip/es6/nodeBuffer.js": "13", "/home/<USER>/www/pizzip/es6/nodeBufferReader.js": "14", "/home/<USER>/www/pizzip/es6/object.js": "15", "/home/<USER>/www/pizzip/es6/signature.js": "16", "/home/<USER>/www/pizzip/es6/stringReader.js": "17", "/home/<USER>/www/pizzip/es6/stringWriter.js": "18", "/home/<USER>/www/pizzip/es6/support.js": "19", "/home/<USER>/www/pizzip/es6/uint8ArrayReader.js": "20", "/home/<USER>/www/pizzip/es6/uint8ArrayWriter.js": "21", "/home/<USER>/www/pizzip/es6/utf8.js": "22", "/home/<USER>/www/pizzip/es6/utils.js": "23", "/home/<USER>/www/pizzip/es6/zipEntries.js": "24", "/home/<USER>/www/pizzip/es6/zipEntry.js": "25"}, {"size": 1199, "mtime": 1746440037099, "results": "26", "hashOfConfig": "27"}, {"size": 1473, "mtime": 1737706686395, "results": "28", "hashOfConfig": "27"}, {"size": 717, "mtime": 1737706686395, "results": "29", "hashOfConfig": "27"}, {"size": 274, "mtime": 1737706686395, "results": "30", "hashOfConfig": "27"}, {"size": 3756, "mtime": 1737706686395, "results": "31", "hashOfConfig": "27"}, {"size": 2909, "mtime": 1746440037099, "results": "32", "hashOfConfig": "27"}, {"size": 285, "mtime": 1737706686395, "results": "33", "hashOfConfig": "27"}, {"size": 2416, "mtime": 1737706686395, "results": "34", "hashOfConfig": "27"}, {"size": 610, "mtime": 1737706686395, "results": "35", "hashOfConfig": "27"}, {"size": 2172, "mtime": 1737706686395, "results": "36", "hashOfConfig": "27"}, {"size": 418, "mtime": 1737706686395, "results": "37", "hashOfConfig": "27"}, {"size": 1050, "mtime": 1737706686395, "results": "38", "hashOfConfig": "27"}, {"size": 232, "mtime": 1737706686395, "results": "39", "hashOfConfig": "27"}, {"size": 536, "mtime": 1746440037099, "results": "40", "hashOfConfig": "27"}, {"size": 26268, "mtime": 1746448031787, "results": "41", "hashOfConfig": "27"}, {"size": 294, "mtime": 1737706686396, "results": "42", "hashOfConfig": "27"}, {"size": 1002, "mtime": 1746440037099, "results": "43", "hashOfConfig": "27"}, {"size": 577, "mtime": 1737706686396, "results": "44", "hashOfConfig": "27"}, {"size": 1111, "mtime": 1747061175650, "results": "45", "hashOfConfig": "27"}, {"size": 677, "mtime": 1746440037099, "results": "46", "hashOfConfig": "27"}, {"size": 844, "mtime": 1737706686396, "results": "47", "hashOfConfig": "27"}, {"size": 5707, "mtime": 1737706686396, "results": "48", "hashOfConfig": "27"}, {"size": 10014, "mtime": 1747061175650, "results": "49", "hashOfConfig": "27"}, {"size": 11249, "mtime": 1746440037099, "results": "50", "hashOfConfig": "27"}, {"size": 11436, "mtime": 1746440037099, "results": "51", "hashOfConfig": "27"}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1fv0i84", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/www/pizzip/es6/arrayReader.js", [], [], "/home/<USER>/www/pizzip/es6/base64.js", [], [], "/home/<USER>/www/pizzip/es6/compressedObject.js", [], [], "/home/<USER>/www/pizzip/es6/compressions.js", [], [], "/home/<USER>/www/pizzip/es6/crc32.js", [], [], "/home/<USER>/www/pizzip/es6/dataReader.js", [], [], "/home/<USER>/www/pizzip/es6/defaults.js", [], [], "/home/<USER>/www/pizzip/es6/deprecatedPublicUtils.js", [], [], "/home/<USER>/www/pizzip/es6/flate.js", [], [], "/home/<USER>/www/pizzip/es6/index.js", [], [], "/home/<USER>/www/pizzip/es6/license_header.js", [], [], "/home/<USER>/www/pizzip/es6/load.js", [], [], "/home/<USER>/www/pizzip/es6/nodeBuffer.js", [], [], "/home/<USER>/www/pizzip/es6/nodeBufferReader.js", [], [], "/home/<USER>/www/pizzip/es6/object.js", [], [], "/home/<USER>/www/pizzip/es6/signature.js", [], [], "/home/<USER>/www/pizzip/es6/stringReader.js", [], [], "/home/<USER>/www/pizzip/es6/stringWriter.js", [], [], "/home/<USER>/www/pizzip/es6/support.js", [], [], "/home/<USER>/www/pizzip/es6/uint8ArrayReader.js", [], [], "/home/<USER>/www/pizzip/es6/uint8ArrayWriter.js", [], [], "/home/<USER>/www/pizzip/es6/utf8.js", [], [], "/home/<USER>/www/pizzip/es6/utils.js", [], [], "/home/<USER>/www/pizzip/es6/zipEntries.js", [], [], "/home/<USER>/www/pizzip/es6/zipEntry.js", [], []]