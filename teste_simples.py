print("TESTE SIMPLES FUNCIONANDO!")

def obter_concessionaria_completa(nome_curto):
    """Converte nome curto da concessionária para nome completo"""
    mapeamento_concessionarias = {
        'Ecovias': 'Concessionária Ecovias dos Imigrantes S/A',
        'Autoban': 'Concessionária do Sistema Anhanguera - Bandeirantes S/A',
        'Tebe': 'Concessionária de Rodovias Tebe S/A',
    }
    
    return mapeamento_concessionarias.get(nome_curto, nome_curto)

# Teste
resultado = obter_concessionaria_completa("Ecovias")
print(f"Teste: 'Ecovias' → '{resultado}'")

resultado2 = obter_concessionaria_completa("Autoban")
print(f"Teste: 'Autoban' → '{resultado2}'")

resultado3 = obter_concessionaria_completa("NaoExiste")
print(f"Teste: 'NaoExiste' → '{resultado3}'")
