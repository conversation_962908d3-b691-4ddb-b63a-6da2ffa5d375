"use strict";

function _typeof(o) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o; }, _typeof(o); }
function _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError("Cannot call a class as a function"); }
function _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, "value" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }
function _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, "prototype", { writable: !1 }), e; }
function _toPropertyKey(t) { var i = _toPrimitive(t, "string"); return "symbol" == _typeof(i) ? i : i + ""; }
function _toPrimitive(t, r) { if ("object" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || "default"); if ("object" != _typeof(i)) return i; throw new TypeError("@@toPrimitive must return a primitive value."); } return ("string" === r ? String : Number)(t); }
var wrapper = require("../module-wrapper.js");
var _require = require("../errors.js"),
  getScopeCompilationError = _require.getScopeCompilationError,
  getCorruptCharactersException = _require.getCorruptCharactersException;
var _require2 = require("../doc-utils.js"),
  utf8ToWord = _require2.utf8ToWord,
  hasCorruptCharacters = _require2.hasCorruptCharacters,
  removeCorruptCharacters = _require2.removeCorruptCharacters;
var _require3 = require("../content-types.js"),
  settingsContentType = _require3.settingsContentType,
  coreContentType = _require3.coreContentType,
  appContentType = _require3.appContentType,
  customContentType = _require3.customContentType;
var NON_LINE_BREAKS_CONTENT_TYPE = [settingsContentType, coreContentType, appContentType, customContentType];
var ftprefix = {
  docx: "w",
  pptx: "a"
};
var Render = /*#__PURE__*/function () {
  function Render() {
    _classCallCheck(this, Render);
    this.name = "Render";
    this.recordRun = false;
    this.recordedRun = [];
  }
  return _createClass(Render, [{
    key: "optionsTransformer",
    value: function optionsTransformer(options, docxtemplater) {
      this.docxtemplater = docxtemplater;
      this.brTag = docxtemplater.fileType === "docx" ? "<w:r><w:br/></w:r>" : "<a:br/>";
      this.prefix = ftprefix[docxtemplater.fileType];
      this.runStartTag = "".concat(this.prefix, ":r");
      this.runPropsStartTag = "".concat(this.prefix, ":rPr");
      return options;
    }
  }, {
    key: "set",
    value: function set(obj) {
      if (obj.compiled) {
        this.compiled = obj.compiled;
      }
      if (obj.data != null) {
        this.data = obj.data;
      }
    }
  }, {
    key: "getRenderedMap",
    value: function getRenderedMap(mapper) {
      for (var from in this.compiled) {
        mapper[from] = {
          from: from,
          data: this.data
        };
      }
      return mapper;
    }
  }, {
    key: "postparse",
    value: function postparse(postparsed, options) {
      var errors = [];
      for (var _i2 = 0; _i2 < postparsed.length; _i2++) {
        var p = postparsed[_i2];
        if (p.type === "placeholder") {
          var tag = p.value;
          try {
            options.cachedParsers[p.lIndex] = this.docxtemplater.parser(tag, {
              tag: p
            });
          } catch (rootError) {
            errors.push(getScopeCompilationError({
              tag: tag,
              rootError: rootError,
              offset: p.offset
            }));
          }
        }
      }
      return {
        postparsed: postparsed,
        errors: errors
      };
    }
  }, {
    key: "render",
    value: function render(part, _ref) {
      var contentType = _ref.contentType,
        scopeManager = _ref.scopeManager,
        linebreaks = _ref.linebreaks,
        nullGetter = _ref.nullGetter,
        fileType = _ref.fileType,
        stripInvalidXMLChars = _ref.stripInvalidXMLChars;
      if (NON_LINE_BREAKS_CONTENT_TYPE.indexOf(contentType) !== -1) {
        // Fixes issue tested in #docprops-linebreak
        linebreaks = false;
      }
      if (linebreaks) {
        this.recordRuns(part);
      }
      if (part.type !== "placeholder" || part.module) {
        return;
      }
      var value;
      try {
        value = scopeManager.getValue(part.value, {
          part: part
        });
      } catch (e) {
        return {
          errors: [e]
        };
      }
      value !== null && value !== void 0 ? value : value = nullGetter(part);
      if (typeof value === "string") {
        if (stripInvalidXMLChars) {
          value = removeCorruptCharacters(value);
        } else if (["docx", "pptx", "xlsx"].indexOf(fileType) !== -1 && hasCorruptCharacters(value)) {
          return {
            errors: [getCorruptCharactersException({
              tag: part.value,
              value: value,
              offset: part.offset
            })]
          };
        }
      }
      if (fileType === "text") {
        return {
          value: value
        };
      }
      return {
        value: linebreaks && typeof value === "string" ? this.renderLineBreaks(value) : utf8ToWord(value)
      };
    }
  }, {
    key: "recordRuns",
    value: function recordRuns(part) {
      if (part.tag === this.runStartTag) {
        this.recordedRun = "";
      } else if (part.tag === this.runPropsStartTag) {
        if (part.position === "start") {
          this.recordRun = true;
          this.recordedRun += part.value;
        }
        if (part.position === "end" || part.position === "selfclosing") {
          this.recordedRun += part.value;
          this.recordRun = false;
        }
      } else if (this.recordRun) {
        this.recordedRun += part.value;
      }
    }
  }, {
    key: "renderLineBreaks",
    value: function renderLineBreaks(value) {
      var result = [];
      var lines = value.split("\n");
      for (var i = 0, len = lines.length; i < len; i++) {
        result.push(utf8ToWord(lines[i]));
        if (i < lines.length - 1) {
          result.push("</".concat(this.prefix, ":t></").concat(this.prefix, ":r>").concat(this.brTag, "<").concat(this.prefix, ":r>").concat(this.recordedRun, "<").concat(this.prefix, ":t").concat(this.docxtemplater.fileType === "docx" ? ' xml:space="preserve"' : "", ">"));
        }
      }
      return result;
    }
  }]);
}();
module.exports = function () {
  return wrapper(new Render());
};