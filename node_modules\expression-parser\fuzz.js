// var assert = require('chai').assert;

// var lex = require('./lex');
var parse = require('./parse');
var echo = require('./echo');

function getFuzz() {
  // var len = Math.random() * 4 | 0,
  var len = 3,
      chars = '';
  while (len--) {
    chars += String.fromCharCode(Math.random() * 100 + 20 | 0);
  }
  return chars;
  // return String.fromCharCode(Math.random() * 255 | 0);
}


var fuzzFor = 100000,
    fuzz,
    out; //,
    // fails = [];
while (--fuzzFor) {
  fuzz = getFuzz();
  try {
    // out = lex(fuzz);
    // out = parse(fuzz);
    out = echo(fuzz);
    // if (!out && !out.length) {  // lexer
    //   console.log('success but no array for', fuzz);
    // }
    // if (!out || out.type !== 'ASTNode') {
    //   console.log('bad success', fuzz, 'got', out);
    // }
    if (out !== fuzz) {
      console.log('echo failed for', fuzz);
    }
  } catch (err) {
    if (!(err instanceof parse.ParseError)) {
      // fails.push(fuzz);
      // throw err;
      console.log('bad failure', fuzz, 'with', err);
    }
  }
}

// fails.sort();
// console.log(fails);

